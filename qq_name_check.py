import asyncio
import aiohttp


async def get_group_member_list(base_url=None, group_id=None, no_cache="true"):
    """
    异步调用 API 获取指定群组的成员列表，支持单个或多个 group_id，并合并结果为一个列表。

    参数:
    - base_url: API 的基础 URL，默认为 None。如果未提供，将使用 "http://127.0.0.1:3000"。
    - group_id: 群组 ID，可以是单个字符串/数字，或一个列表/数组。默认为 None，如果未提供，将打印错误并返回空列表。
    - no_cache: 是否不使用缓存，默认为 "true"（字符串）。

    返回:
    - 成员列表（数组），合并了所有指定群组的成员。如果 API 调用失败，返回空列表或部分结果。

    示例调用:
    - 单个 group_id: members = await get_group_member_list(group_id="978880814")
    - 多个 group_id: members = await get_group_member_list(group_id=["978880814", "976420087"])
    """
    if base_url is None:
        base_url = "http://127.0.0.1:3000"  # 默认 API 地址
    if group_id is None:
        print("请提供群号")
        return []

    # 检查 group_id 类型，如果是单个值，转换为列表以统一处理
    if not isinstance(group_id, (list, tuple)):
        group_id_list = [group_id]  # 转换为列表
    else:
        group_id_list = group_id  # 已经是列表

    all_members = []  # 用于存储所有成员列表

    async with aiohttp.ClientSession() as session:
        tasks = []
        for gid in group_id_list:
            # 直接在循环中定义并添加异步任务
            async def fetch_task(gid=gid):  # 使用默认参数避免闭包问题
                api_url = f"{base_url}/get_group_member_list"
                payload = {
                    "group_id": gid,
                    "no_cache": no_cache
                }
                async with session.post(api_url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("status") == "ok":
                            return data.get("data", [])  # 返回成员列表
                        else:
                            print(f"API 返回错误 for group_id {gid}: {data.get('message', '未知错误')}")
                            return []
                    else:
                        print(f"请求失败 for group_id {gid}，状态码: {response.status}")
                        return []

            tasks.append(fetch_task())  # 添加任务

        results = await asyncio.gather(*tasks, return_exceptions=True)  # 并发执行任务，捕获异常

        for result in results:
            if isinstance(result, Exception):
                print(f"API 调用异常: {str(result)}")
            elif result is not None:
                all_members.extend(result)  # 合并成员列表

    return all_members


async def check_card_in_members(card, members):
    """
    异步检查成员列表中是否存在指定的群昵称（card），使用不区分大小写的子字符串匹配。

    参数:
    - card: 要检查的群昵称（字符串），例如 "ccyniuniu"。会进行子字符串匹配（例如 "管理Ccyniuniu" 能被匹配）。
    - members: 成员列表（数组），由 get_group_member_list 函数返回。

    返回:
    - True: 如果昵称（或其子字符串）存在于成员列表中（不区分大小写）。
    - False: 如果昵称不存在。

    示例输出:
    - 如果匹配到，会打印 "{card} 在群里" 并返回 True。
    - 如果未匹配到，会打印 "{card} 不在群里" 并返回 False。
    """
    for member in members:
        member_card = member.get("card", "").lower()  # 将成员的 card 转为小写
        search_card = card.lower()  # 将输入的 card 转为小写
        if search_card in member_card:  # 子字符串匹配
            print(f"{card} 在群里")  # 输出匹配信息
            return True
    # 遍历完未找到匹配
    print(f"{card} 不在群里")  # 输出未匹配信息
    return False


# 示例异步调用，需要使用 asyncio.run()
async def main():
    # 示例 1: 使用单个 group_id
    members_single = await get_group_member_list(group_id="978880814")
    is_in_group_single = await check_card_in_members(card="halohan999", members=members_single)
    print(f"单个群组检查返回: {is_in_group_single}")

    # 示例 2: 使用多个 group_id，合并列表后检查
    members_multiple = await get_group_member_list(group_id=["978880814", "976420087"])  # 替换为实际 ID
    is_in_group_multiple = await check_card_in_members(card="akakak", members=members_multiple)
    print(f"多个群组合并检查返回: {is_in_group_multiple}")


# 运行异步主函数
if __name__ == "__main__":
    asyncio.run(main())