import time
import requests
import json


def get_gameid_by_name(server_name):
    if server_name is None:
        return json.dumps({"success": False, "result": "找不到服务器，请纠正查询参数"})

    url = f"https://api.gametools.network/bfv/servers/?name={server_name}"
    max_retries = 3  # 最大重试次数
    timeout = 5  # 超时时间，单位：秒
    retry_delay = 1  # 重试之间的延迟时间，单位：秒

    for attempt in range(1, max_retries + 1):
        try:
            # 发送GET请求，并设置超时
            response = requests.get(url, timeout=timeout)
            if response.status_code == 200:
                try:
                    # 解析JSON数据
                    data = response.json()
                    servers = data.get('servers', [])  # 获取servers列表，如果不存在则返回空列表
                    if len(servers) == 1:
                        game_id = servers[0].get('gameId', None)
                        if game_id is not None:
                            return json.dumps({"success": True, "result": game_id})  # 返回成功和gameId
                        else:
                            return json.dumps(
                                {"success": False, "result": "在返回数据中找不到gameid"})  # gameId不存在
                    else:
                        return json.dumps(
                            {"success": False, "result": "找到多个匹配的服务器，请纠正查询参数"})  # 服务器数量不是1
                except json.JSONDecodeError as e:
                    error_msg = f"JSON decode error: {str(e)}"
                    print(f"尝试 {attempt}: {error_msg}")
                    if attempt < max_retries:
                        print(f"等待 {retry_delay} 秒后重试...")
                        time.sleep(retry_delay)
                    return json.dumps({"success": False, "result": error_msg})
            else:
                # HTTP状态码不是200
                error_msg = f"HTTP error with status code: {response.status_code}"
                print(f"尝试 {attempt}: {error_msg}")
                if attempt < max_retries:
                    print(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
        except requests.exceptions.RequestException as e:
            # 请求异常（如超时、连接错误）
            error_msg = f"Request exception: {str(e)}"
            print(f"尝试 {attempt}: {error_msg}")
            if attempt < max_retries:
                print(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)

    # 所有重试失败
    return json.dumps({"success": False, "result": "所有尝试都失败了，请重试或联系管理员"})


# 示例测试代码
test1 = "2636"
data1 = get_gameid_by_name(test1)
print(f"查询结果: {data1}")  # 预期: {"success": false, "result": "No server or multiple servers found"}，因为服务器数量为4

test2 = "xx"
data2 = get_gameid_by_name(test2)
print(f"查询结果: {data2}")  # 预期: {"success": true, "result": "10269219930282"}，因为服务器数量为1

# 额外测试：服务器名称为None
data3 = get_gameid_by_name(None)
print(f"查询结果: {data3}")  # 预期: {"success": false, "result": "Server name is None"}

# 运行示例
# 如果您在命令行运行，输出会是JSON字符串