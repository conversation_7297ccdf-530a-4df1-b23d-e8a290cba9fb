import os

import requests

_default_api_url ="http://127.0.0.1:3000"
_default_group_id="982726092"
_default_token="shikanoko"

def send_group_msg(base_url=None, group_id=None, text=None, image_file=None, image_url=None, at_someone=None, reply_some=None, access_token=None):
    """
    发送群消息函数，支持 token 鉴权（将 access_token 作为查询参数）。

    参数:
    - base_url: API 的基础 URL，例如 "http://127.0.0.1:3000"。
    - group_id: 群组 ID（字符串或数字）。
    - text: 可选的文本消息（字符串）。
    - image_file: 可选的图片文件路径（字符串，例如 "D:/a.jpg"，会自动转换为 "file://D:/a.jpg"）。
    - access_token: 必填的访问 token，用于鉴权，例如 "password"。

    返回:
    - 如果成功，返回消息 ID（数字）。
    - 如果失败，抛出异常。
    """
    # 使用默认值如果参数为空
    if base_url is None:
        base_url = _default_api_url
    if group_id is None:
        group_id = _default_group_id
    if access_token is None:
        access_token = _default_token

    # 检查必填参数
    if not access_token:
        raise ValueError("access_token 是必填参数。请提供有效的 token。")
    if not base_url:
        raise ValueError("base_url 是必填参数。请提供有效的 API URL。")
    if not group_id:
        raise ValueError("group_id 是必填参数。请提供有效的群组 ID。")

    # 构建消息数组
    message_array = []

    #//第一个必须为reply
    if reply_some:
        message_array.append({
            "type": "reply",
            "data": {
                "id": reply_some
            }
        })

    if at_someone:
        message_array.append({
            "type": "at",
            "data": {
                "qq": at_someone
            }
        })

    if text:
        message_array.append({
            "type": "text",
            "data": {
                "text": text  # 文本内容
            }
        })

    if image_file:
        # 将 image_file 转换为绝对路径
        abs_image_file = os.path.abspath(image_file)  # 获取绝对路径
        if not os.path.exists(abs_image_file):  # 检查文件是否存在
            raise FileNotFoundError(f"图片文件不存在: {abs_image_file}")

        message_array.append({
            "type": "image",
            "data": {
                "file": f"file://{abs_image_file}"  # 使用绝对路径
            }
        })

    if image_url:
        message_array.append({
            "type": "image",
            "data": {
                "file": f"{image_url}"  # 使用绝对路径
            }
        })

    if not message_array:
        raise ValueError("没有提供任何消息内容。请至少提供文本或图片。")

    # 构建请求体（不包括 access_token）
    payload = {
        "group_id": str(group_id),  # 确保 group_id 转换为字符串
        "message": message_array
    }

    try:
        # 发送 POST 请求，将 access_token 作为查询参数
        response = requests.post(
            f"{base_url}/send_group_msg",
            json=payload,  # 请求体
            params={"access_token": access_token}  # 查询参数
        )
        response.raise_for_status()  # 检查 HTTP 错误

        data = response.json()

        if data.get("status") == "ok":
            return data["data"].get("message_id")  # 返回消息 ID
        else:
            raise Exception(f"API 错误: {data.get('message')}，错误码: {data.get('retcode')}")

    except requests.exceptions.RequestException as e:
        raise Exception(f"网络请求失败: {str(e)}")
    except FileNotFoundError as e:
        raise Exception(f"文件错误: {str(e)}")

if __name__ == "__main__":
    reply = "759506557"
    at_someone = "2720216977"
    image_url= "https://multimedia.nt.qq.com.cn/download?appid=1407&fileid=EhRk1hS1r8LSqq913eisyHrqbV75oxiKwQEg_woostz9-K_ejAMyBHByb2RQgL2jAVoQ5k6HKacWxM0k_jA2rpD-u3oCMYc&rkey=CAISKKSBekjVG1fMCqS9YjEaPXXRvhXO9y_1Ro-SLJh7s5My6T8W_6gdEp0"
    try:
        # 发送文本消息
        message_id = send_group_msg(text="这是一条测试文本",reply_some=reply,at_someone=at_someone,image_url=image_url)
        print(f"发送成功，消息 ID: {message_id}")

    except Exception as e:
        print(f"错误: {str(e)}")
