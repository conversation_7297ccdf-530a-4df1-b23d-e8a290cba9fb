import os
import requests

_default_api_url = "http://127.0.0.1:3000"
_default_group_id = "982726092"
_default_token = "shikanoko"


def get_group_msg_history(base_url=None, group_id=None, message_seq=None, count=None, reverseOrder=False,
                          access_token=None):
    """
    获取群历史消息函数，支持 token 鉴权（将 access_token 作为查询参数）。
    参数:
    - base_url: API 的基础 URL，例如 "http://127.0.0.1:3000"。
    - group_id: 群组 ID（字符串或数字）。
    - message_seq: 消息序列号（字符串或数字），0 表示最新消息。
    - count: 获取的消息数量（数字）。
    - reverseOrder: 是否倒序（布尔值）。
    - access_token: 访问 token，用于鉴权，例如 "password"。
    返回:
    - 如果成功，返回 API 响应中的 data 对象（包含 messages 数组）。
    - 如果失败，抛出异常。
    """
    # 使用默认值如果参数为空
    if base_url is None:
        base_url = _default_api_url
    if group_id is None:
        group_id = _default_group_id
    if access_token is None:
        access_token = _default_token

    # 检查必填参数
    required_params = {
        'group_id': group_id,
        'message_seq': message_seq,
        'count': count,
        'reverseOrder': reverseOrder
    }
    for param, value in required_params.items():
        if value is None:
            raise ValueError(f"{param} 是必填参数。请提供有效的 {param}。")

    # 构建请求体
    payload = {
        "group_id": str(group_id),  # 确保转换为字符串
        "message_seq": str(message_seq),  # 确保转换为字符串
        "count": count,  # 数字
        "reverseOrder": reverseOrder  # 布尔值
    }

    try:
        # 发送 POST 请求，将 access_token 作为查询参数
        response = requests.post(
            f"{base_url}/get_group_msg_history",
            json=payload,  # 请求体
            params={"access_token": access_token}  # 查询参数
        )
        response.raise_for_status()  # 检查 HTTP 错误
        data = response.json()
        if data.get("status") == "ok":
            return data.get("data")  # 返回 data 对象，包含 messages 数组
        else:
            raise Exception(f"API 错误: {data.get('message')}，错误码: {data.get('retcode')}")
    except requests.exceptions.RequestException as e:
        raise Exception(f"网络请求失败: {str(e)}")
    except ValueError as e:
        raise Exception(f"参数错误: {str(e)}")

def get_group_member_info(base_url=None, group_id=None, message_seq=None, count=None, reverseOrder=False,
                          access_token=None):
    """
    获取群历史消息函数，支持 token 鉴权（将 access_token 作为查询参数）。
    参数:
    - base_url: API 的基础 URL，例如 "http://127.0.0.1:3000"。
    - group_id: 群组 ID（字符串或数字）。
    - message_seq: 消息序列号（字符串或数字），0 表示最新消息。
    - count: 获取的消息数量（数字）。
    - reverseOrder: 是否倒序（布尔值）。
    - access_token: 访问 token，用于鉴权，例如 "password"。
    返回:
    - 如果成功，返回 API 响应中的 data 对象（包含 messages 数组）。
    - 如果失败，抛出异常。
    """
    # 使用默认值如果参数为空
    if base_url is None:
        base_url = _default_api_url
    if group_id is None:
        group_id = _default_group_id
    if access_token is None:
        access_token = _default_token

    user_id = "3989549945"

    # 检查必填参数
    required_params = {
        'group_id': group_id,
        "user_id": user_id,
        "no_cache": True
    }
    for param, value in required_params.items():
        if value is None:
            raise ValueError(f"{param} 是必填参数。请提供有效的 {param}。")

    # 构建请求体
    payload = {
        'group_id': group_id,
        "user_id": user_id,
        "no_cache": True
    }

    try:
        # 发送 POST 请求，将 access_token 作为查询参数
        response = requests.post(
            f"{base_url}/get_group_member_info",
            json=payload,  # 请求体
            params={"access_token": access_token}  # 查询参数
        )
        response.raise_for_status()  # 检查 HTTP 错误
        data = response.json()
        if data.get("status") == "ok":
            return data.get("data")  # 返回 data 对象，包含 messages 数组
        else:
            raise Exception(f"API 错误: {data.get('message')}，错误码: {data.get('retcode')}")
    except requests.exceptions.RequestException as e:
        raise Exception(f"网络请求失败: {str(e)}")
    except ValueError as e:
        raise Exception(f"参数错误: {str(e)}")


if __name__ == "__main__":
    try:
        # 示例：获取群历史消息
        # 使用默认参数，message_seq=0 表示获取最新消息，count=10 表示获取10条消息，倒序
        result = get_group_member_info()
        bot_data = get_group_member_info()
        nickname = bot_data.get('nickname','')
        print(nickname)
        print("获取成功，结果:", result)  # result 包含 messages 数组
    except Exception as e:
        print(f"错误: {str(e)}")