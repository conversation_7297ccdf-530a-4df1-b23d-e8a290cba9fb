2025-05-16 02:14:35,889 - __main__ - INFO - Niu<PERSON>iuBot配置加载成功
2025-05-16 02:14:35,889 - __main__ - INFO - 当前的NiuNiuBot配置:
Key               | Value
------------------------
address           | http://127.0.0.1:3000
token             | shikanoko
server_group_name | 2636
qq_bot            | 3989549945
admin_qq          | 350362423
check_admin       | 2720216977
active_groups     | [976420087]
public_groups     | 978880814
nfj_group         | 1037749375
nfj_bot           | 2720216977
ban_group         | 559046176
nfj_stop          | False
nfj_on            | True
our_server        | {'s1': '10313885910047', 's2': '10312665510199', 's3': '10269676270097', 's4': '10295688560381', 's5': '10306070430418', 'touhou': '10298171410685'}
nfj_server        | {'s1': '10307748850667', 's2': '10294976540859', 's3': '10269676270097', 's4': '10295688560381', 'touhou': '10298171410685'}
reminder          | True
reminder_groups   | [976420087]
reminder_set      | 
ai_judgement      | https://bfvai.yesu.eu.org/shikanoko/ai_judgment

2025-05-16 02:14:35,890 - __main__ - INFO - 使用绑定地址: 127.0.0.1, 端口: 18889
2025-05-16 02:14:35,890 - __main__ - INFO - 动态命令 's1' 注册成功
2025-05-16 02:14:35,890 - __main__ - INFO - 动态命令 's2' 注册成功
2025-05-16 02:14:35,890 - __main__ - INFO - 动态命令 's3' 注册成功
2025-05-16 02:14:35,890 - __main__ - INFO - 动态命令 's4' 注册成功
2025-05-16 02:14:35,890 - __main__ - INFO - 动态命令 's5' 注册成功
2025-05-16 02:14:35,890 - __main__ - INFO - 动态命令 'touhou' 注册成功
2025-05-16 02:14:35,890 - __main__ - INFO - 正在启动机器人服务...
2025-05-16 02:14:35,891 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-16 02:14:35,891 - __main__ - INFO - 转发机器人服务已启动
2025-05-16 02:14:35,892 - __main__ - INFO - 提醒任务启动：时间段从 7:1 到 0:59，间隔 60 分钟。
2025-05-16 02:14:40,184 - __main__ - DEBUG - 收到事件: {'self_id': 3989549945, 'user_id': 2720216977, 'time': 1747332880, 'message_id': 1850330701, 'message_seq': 1850330701, 'real_id': 1850330701, 'real_seq': '1954', 'message_type': 'group', 'sender': {'user_id': 2720216977, 'nickname': '呱呱吃瓜瓜', 'card': 'seeseeyourniuzi', 'role': 'owner'}, 'raw_message': 'cnf s1', 'font': 14, 'sub_type': 'normal', 'message': [{'type': 'text', 'data': {'text': 'cnf s1'}}], 'message_format': 'array', 'post_type': 'message', 'group_id': 976420087, 'raw': {'msgId': '7504737576647608276', 'msgRandom': '831589465', 'msgSeq': '1954', 'cntSeq': '0', 'chatType': 2, 'msgType': 2, 'subMsgType': 1, 'sendType': 0, 'senderUid': 'u_wuwfP8yEMbxrGb627ALAkw', 'peerUid': '976420087', 'channelId': '', 'guildId': '', 'guildCode': '0', 'fromUid': '0', 'fromAppid': '0', 'msgTime': '1747332880', 'msgMeta': {}, 'sendStatus': 2, 'sendRemarkName': '', 'sendMemberName': 'seeseeyourniuzi', 'sendNickName': '', 'guildName': '', 'channelName': '', 'elements': [{'elementType': 1, 'elementId': '7504737576647608275', 'elementGroupId': 0, 'extBufForUI': {}, 'textElement': {'content': 'cnf s1', 'atType': 0, 'atUid': '0', 'atTinyId': '0', 'atNtUid': '', 'subElementType': 0, 'atChannelId': '0', 'linkInfo': None, 'atRoleId': '0', 'atRoleColor': 0, 'atRoleName': '', 'needNotify': 0}, 'faceElement': None, 'marketFaceElement': None, 'replyElement': None, 'picElement': None, 'pttElement': None, 'videoElement': None, 'grayTipElement': None, 'arkElement': None, 'fileElement': None, 'liveGiftElement': None, 'markdownElement': None, 'structLongMsgElement': None, 'multiForwardMsgElement': None, 'giphyElement': None, 'walletElement': None, 'inlineKeyboardElement': None, 'textGiftElement': None, 'calendarElement': None, 'yoloGameResultElement': None, 'avRecordElement': None, 'structMsgElement': None, 'faceBubbleElement': None, 'shareLocationElement': None, 'tofuRecordElement': None, 'taskTopMsgElement': None, 'recommendedMsgElement': None, 'actionBarElement': None, 'prologueMsgElement': None, 'forwardMsgElement': None}], 'records': [], 'emojiLikesList': [], 'commentCnt': '0', 'directMsgFlag': 0, 'directMsgMembers': [], 'peerName': '测试2', 'freqLimitInfo': None, 'editable': False, 'avatarMeta': '', 'avatarPendant': '', 'feedId': '', 'roleId': '0', 'timeStamp': '0', 'clientIdentityInfo': None, 'isImportMsg': False, 'atType': 0, 'roleType': 0, 'fromChannelRoleInfo': {'roleId': '0', 'name': '', 'color': 0}, 'fromGuildRoleInfo': {'roleId': '0', 'name': '', 'color': 0}, 'levelRoleInfo': {'roleId': '0', 'name': '', 'color': 0}, 'recallTime': '0', 'isOnlineMsg': True, 'generalFlags': {}, 'clientSeq': '0', 'fileGroupSize': None, 'foldingInfo': None, 'multiTransInfo': None, 'senderUin': '2720216977', 'peerUin': '976420087', 'msgAttrs': {}, 'anonymousExtInfo': None, 'nameType': 0, 'avatarFlag': 0, 'extInfoForUI': None, 'personalMedal': None, 'categoryManage': 0, 'msgEventInfo': None, 'sourceType': 1, 'id': 1850330701}}
2025-05-16 02:14:40,185 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-16 02:14:40,185 - __main__ - INFO - 已启动服务器 s1 的暖服状态监控任务
2025-05-16 02:14:40,186 - __main__ - INFO - [s1] 开始监控，每分钟检查，超时30分钟。
2025-05-16 02:14:40,186 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 02:14:40,756 - __main__ - WARNING - [s1] 暖服监控任务被取消。
2025-05-16 02:14:40,756 - __main__ - DEBUG - 收到事件: {'self_id': 3989549945, 'user_id': 3989549945, 'time': 1747332880, 'message_id': 1421547359, 'message_seq': 1421547359, 'real_id': 1421547359, 'real_seq': '1955', 'message_type': 'group', 'sender': {'user_id': 3989549945, 'nickname': '劉德華', 'card': '', 'role': 'member'}, 'raw_message': '[CQ:at,qq=-1574750319] 已开始监控服务器 s1 的暖服状态。', 'font': 14, 'sub_type': 'normal', 'message': [{'type': 'at', 'data': {'qq': '-1574750319'}}, {'type': 'text', 'data': {'text': ' 已开始监控服务器 s1 的暖服状态。'}}], 'message_format': 'array', 'post_type': 'message_sent', 'message_sent_type': 'self', 'group_id': 976420087, 'target_id': 976420087, 'raw': {'msgId': '7578954610466900471', 'msgRandom': '1026834013', 'msgSeq': '1955', 'cntSeq': '0', 'chatType': 2, 'msgType': 2, 'subMsgType': 1, 'sendType': 1, 'senderUid': 'u_21pfMF7bgFW_1a6tsQLA3A', 'peerUid': '976420087', 'channelId': '', 'guildId': '7578954610466900470', 'guildCode': '0', 'fromUid': '0', 'fromAppid': '0', 'msgTime': '1747332880', 'msgMeta': {}, 'sendStatus': 2, 'sendRemarkName': '', 'sendMemberName': '', 'sendNickName': '劉德華', 'guildName': '', 'channelName': '', 'elements': [{'elementType': 1, 'elementId': '7504737576647608278', 'elementGroupId': 0, 'extBufForUI': {}, 'textElement': {'content': '@seeseeyourniuzi', 'atType': 2, 'atUid': '-1574750319', 'atTinyId': '0', 'atNtUid': 'u_wuwfP8yEMbxrGb627ALAkw', 'subElementType': 0, 'atChannelId': '0', 'linkInfo': None, 'atRoleId': '0', 'atRoleColor': 0, 'atRoleName': '', 'needNotify': 0}, 'faceElement': None, 'marketFaceElement': None, 'replyElement': None, 'picElement': None, 'pttElement': None, 'videoElement': None, 'grayTipElement': None, 'arkElement': None, 'fileElement': None, 'liveGiftElement': None, 'markdownElement': None, 'structLongMsgElement': None, 'multiForwardMsgElement': None, 'giphyElement': None, 'walletElement': None, 'inlineKeyboardElement': None, 'textGiftElement': None, 'calendarElement': None, 'yoloGameResultElement': None, 'avRecordElement': None, 'structMsgElement': None, 'faceBubbleElement': None, 'shareLocationElement': None, 'tofuRecordElement': None, 'taskTopMsgElement': None, 'recommendedMsgElement': None, 'actionBarElement': None, 'prologueMsgElement': None, 'forwardMsgElement': None}, {'elementType': 1, 'elementId': '7504737576647608279', 'elementGroupId': 0, 'extBufForUI': {}, 'textElement': {'content': ' 已开始监控服务器 s1 的暖服状态。', 'atType': 0, 'atUid': '0', 'atTinyId': '0', 'atNtUid': '', 'subElementType': 0, 'atChannelId': '0', 'linkInfo': None, 'atRoleId': '0', 'atRoleColor': 0, 'atRoleName': '', 'needNotify': 0}, 'faceElement': None, 'marketFaceElement': None, 'replyElement': None, 'picElement': None, 'pttElement': None, 'videoElement': None, 'grayTipElement': None, 'arkElement': None, 'fileElement': None, 'liveGiftElement': None, 'markdownElement': None, 'structLongMsgElement': None, 'multiForwardMsgElement': None, 'giphyElement': None, 'walletElement': None, 'inlineKeyboardElement': None, 'textGiftElement': None, 'calendarElement': None, 'yoloGameResultElement': None, 'avRecordElement': None, 'structMsgElement': None, 'faceBubbleElement': None, 'shareLocationElement': None, 'tofuRecordElement': None, 'taskTopMsgElement': None, 'recommendedMsgElement': None, 'actionBarElement': None, 'prologueMsgElement': None, 'forwardMsgElement': None}], 'records': [], 'emojiLikesList': [], 'commentCnt': '0', 'directMsgFlag': 0, 'directMsgMembers': [], 'peerName': '测试2', 'freqLimitInfo': None, 'editable': True, 'avatarMeta': '', 'avatarPendant': '', 'feedId': '', 'roleId': '0', 'timeStamp': '0', 'clientIdentityInfo': None, 'isImportMsg': False, 'atType': 2, 'roleType': 0, 'fromChannelRoleInfo': {'roleId': '0', 'name': '', 'color': 0}, 'fromGuildRoleInfo': {'roleId': '0', 'name': '', 'color': 0}, 'levelRoleInfo': {'roleId': '0', 'name': '', 'color': 0}, 'recallTime': '0', 'isOnlineMsg': False, 'generalFlags': {}, 'clientSeq': '34840', 'fileGroupSize': None, 'foldingInfo': None, 'multiTransInfo': None, 'senderUin': '3989549945', 'peerUin': '976420087', 'msgAttrs': {}, 'anonymousExtInfo': None, 'nameType': 0, 'avatarFlag': 0, 'extInfoForUI': None, 'personalMedal': None, 'categoryManage': 0, 'msgEventInfo': None, 'sourceType': 0, 'id': 1421547359}}
2025-05-16 02:14:40,756 - __main__ - INFO - [s1] finally 执行了 - 任务结束.
2025-05-16 02:14:40,757 - __main__ - INFO - [s1] 停止监控任务。
2025-05-16 02:20:51,505 - __main__ - INFO - NiuNiuBot配置加载成功
2025-05-16 02:20:51,505 - __main__ - INFO - 当前的NiuNiuBot配置:
Key               | Value
------------------------
address           | http://127.0.0.1:3000
token             | shikanoko
server_group_name | 2636
qq_bot            | 3989549945
admin_qq          | 350362423
check_admin       | 2720216977
active_groups     | [976420087]
public_groups     | 978880814
nfj_group         | 1037749375
nfj_bot           | 2720216977
ban_group         | 559046176
nfj_stop          | False
nfj_on            | True
our_server        | {'s1': '10313885910047', 's2': '10312665510199', 's3': '10269676270097', 's4': '10295688560381', 's5': '10306070430418', 'touhou': '10298171410685'}
nfj_server        | {'s1': '10307748850667', 's2': '10294976540859', 's3': '10269676270097', 's4': '10295688560381', 'touhou': '10298171410685'}
reminder          | True
reminder_groups   | [976420087]
reminder_set      | 
ai_judgement      | https://bfvai.yesu.eu.org/shikanoko/ai_judgment

2025-05-16 02:20:51,506 - __main__ - INFO - 使用绑定地址: 127.0.0.1, 端口: 18889
2025-05-16 02:20:51,506 - __main__ - INFO - 等待 asyncio 事件循环启动...
2025-05-16 02:20:51,506 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-16 02:20:51,507 - __main__ - INFO - Asyncio 事件循环线程启动。
2025-05-16 02:20:51,507 - __main__ - INFO - 主 asyncio 任务启动。
2025-05-16 02:20:51,507 - __main__ - INFO - 提醒循环任务已在主循环中启动。
2025-05-16 02:20:51,507 - __main__ - INFO - 动态命令 's1' 注册成功
2025-05-16 02:20:51,507 - __main__ - INFO - 提醒任务启动。
2025-05-16 02:20:51,507 - __main__ - INFO - 动态命令 's2' 注册成功
2025-05-16 02:20:51,508 - __main__ - INFO - 动态命令 's3' 注册成功
2025-05-16 02:20:51,508 - __main__ - INFO - 动态命令 's4' 注册成功
2025-05-16 02:20:51,508 - __main__ - INFO - 动态命令 's5' 注册成功
2025-05-16 02:20:51,508 - __main__ - INFO - 动态命令 'touhou' 注册成功
2025-05-16 02:20:51,508 - __main__ - INFO - 正在启动 Flask web 服务...
2025-05-16 02:20:54,066 - __main__ - ERROR - game_id 请求API失败: Cannot connect to host api.bfvrobot.net:443 ssl:default [指定的网络名不再可用。]
Traceback (most recent call last):
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 461, in finish_socket_func
    return ov.getresult()
           ^^^^^^^^^^^^^^
OSError: [WinError 64] 指定的网络名不再可用。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1122, in _wrap_create_connection
    return await self._loop.create_connection(*args, **kwargs, sock=sock)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\base_events.py", line 1126, in create_connection
    transport, protocol = await self._create_connection_transport(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\base_events.py", line 1159, in _create_connection_transport
    await waiter
  File "D:\project\python312\Lib\asyncio\proactor_events.py", line 286, in _loop_reading
    length = fut.result()
             ^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 798, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 465, in finish_socket_func
    raise ConnectionResetError(*exc.args)
ConnectionResetError: [WinError 64] 指定的网络名不再可用。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\bfv_ai_bot\niuniubot.py", line 59, in update_game_id
    async with session.get(url) as response:
  File "D:\project\python312\Lib\site-packages\aiohttp\client.py", line 1425, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\client.py", line 703, in _request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 548, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1056, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1400, in _create_direct_connection
    raise last_exc
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1369, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1130, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.bfvrobot.net:443 ssl:default [指定的网络名不再可用。]
2025-05-16 02:20:54,075 - __main__ - ERROR - reminder_set 配置无效，使用默认提醒时间。
2025-05-16 02:20:54,075 - __main__ - INFO - 下一次提醒时间: 2025-05-16 07:00:00 (等待 16745 秒)
2025-05-16 02:21:09,342 - __main__ - INFO - 接收到命令: cnf, 参数: s1, from user: 2720216977, group: 976420087
2025-05-16 02:21:09,342 - __main__ - INFO - 已启动服务器 s1 的暖服状态监控任务
2025-05-16 02:21:09,343 - __main__ - INFO - [s1] 开始监控，每分钟检查，超时30分钟。
2025-05-16 02:21:09,344 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 02:21:14,706 - __main__ - INFO - 接收到命令: cnf, 参数: , from user: 2720216977, group: 976420087
2025-05-16 02:21:21,927 - __main__ - INFO - 接收到命令: scnf, 参数: s1, from user: 2720216977, group: 976420087
2025-05-16 02:21:21,928 - __main__ - WARNING - [s1] 暖服监控任务被取消。
2025-05-16 02:21:21,928 - __main__ - INFO - [s1] finally 执行了 - 任务结束.
2025-05-16 02:21:21,928 - __main__ - INFO - [s1] 停止监控任务。
2025-05-16 02:21:22,423 - __main__ - INFO - 发送取消信号给服务器 s1 的监控任务
2025-05-16 02:21:34,033 - __main__ - INFO - 接收到命令: cnf, 参数: , from user: 2720216977, group: 976420087
2025-05-16 02:23:45,571 - __main__ - INFO - Flask web 服务已停止。
2025-05-16 02:23:46,970 - __main__ - INFO - NiuNiuBot配置加载成功
2025-05-16 02:23:46,971 - __main__ - INFO - 当前的NiuNiuBot配置:
Key               | Value
------------------------
address           | http://127.0.0.1:3000
token             | shikanoko
server_group_name | 2636
qq_bot            | 3989549945
admin_qq          | 350362423
check_admin       | 2720216977
active_groups     | [976420087]
public_groups     | 978880814
nfj_group         | 1037749375
nfj_bot           | 2720216977
ban_group         | 559046176
nfj_stop          | False
nfj_on            | True
our_server        | {'s1': '10313885910047', 's2': '10312665510199', 's3': '10269676270097', 's4': '10295688560381', 's5': '10306070430418', 'touhou': '10298171410685'}
nfj_server        | {'s1': '10307748850667', 's2': '10294976540859', 's3': '10269676270097', 's4': '10295688560381', 'touhou': '10298171410685'}
reminder          | True
reminder_groups   | [976420087]
reminder_set      | 
ai_judgement      | https://bfvai.yesu.eu.org/shikanoko/ai_judgment

2025-05-16 02:23:46,972 - __main__ - INFO - 使用绑定地址: 127.0.0.1, 端口: 18889
2025-05-16 02:23:46,973 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-16 02:23:46,973 - __main__ - INFO - 等待 asyncio 事件循环启动...
2025-05-16 02:23:46,974 - __main__ - INFO - Asyncio 事件循环线程启动。
2025-05-16 02:23:46,974 - __main__ - INFO - 主 asyncio 任务启动。
2025-05-16 02:23:46,974 - __main__ - INFO - 提醒循环任务已在主循环中启动。
2025-05-16 02:23:46,975 - __main__ - INFO - 动态命令 's1' 注册成功
2025-05-16 02:23:46,975 - __main__ - INFO - 提醒任务启动。
2025-05-16 02:23:46,975 - __main__ - INFO - 动态命令 's2' 注册成功
2025-05-16 02:23:46,976 - __main__ - INFO - 动态命令 's3' 注册成功
2025-05-16 02:23:46,977 - __main__ - INFO - 动态命令 's4' 注册成功
2025-05-16 02:23:46,977 - __main__ - INFO - 动态命令 's5' 注册成功
2025-05-16 02:23:46,977 - __main__ - INFO - 动态命令 'touhou' 注册成功
2025-05-16 02:23:46,977 - __main__ - INFO - 正在启动 Flask web 服务...
2025-05-16 02:23:50,334 - __main__ - INFO - config.json 加载成功
2025-05-16 02:23:50,334 - __main__ - INFO - s1 的 gameId 未变化: 10313885910047
2025-05-16 02:23:50,334 - __main__ - INFO - s2 的 gameId 未变化: 10312665510199
2025-05-16 02:23:50,334 - __main__ - INFO - s3 的 gameId 未变化: 10269676270097
2025-05-16 02:23:50,334 - __main__ - INFO - s4 的 gameId 未变化: 10295688560381
2025-05-16 02:23:50,334 - __main__ - INFO - s5 的 gameId 未变化: 10306070430418
2025-05-16 02:23:50,334 - __main__ - WARNING - 未找到匹配的服务器: touhou
2025-05-16 02:23:50,335 - __main__ - INFO - NiuNiuBot 配置重新加载成功
2025-05-16 02:23:50,335 - __main__ - ERROR - reminder_set 配置无效，使用默认提醒时间。
2025-05-16 02:23:50,335 - __main__ - INFO - 下一次提醒时间: 2025-05-16 07:00:00 (等待 16569 秒)
2025-05-16 02:24:14,166 - __main__ - INFO - 接收到命令: cnf, 参数: s1, from user: 2720216977, group: 976420087
2025-05-16 02:24:14,167 - __main__ - INFO - 已启动服务器 s1 的暖服状态监控任务
2025-05-16 02:24:14,168 - __main__ - INFO - [s1] 开始监控，每分钟检查，超时30分钟。
2025-05-16 02:24:14,168 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 02:24:27,480 - __main__ - INFO - 接收到命令: cnf, 参数: , from user: 2720216977, group: 976420087
2025-05-16 02:25:14,172 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 02:25:14,175 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 02:25:15,246 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10313885910047 HTTP/1.1" 201 851
2025-05-16 02:25:15,246 - __main__ - ERROR - 尝试 1: 服务器玩家列表请求失败，状态码: 201
2025-05-16 02:25:15,246 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 02:25:17,247 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 02:25:18,242 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10313885910047 HTTP/1.1" 201 851
2025-05-16 02:25:18,242 - __main__ - ERROR - 尝试 2: 服务器玩家列表请求失败，状态码: 201
2025-05-16 02:25:18,243 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 02:25:20,243 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 02:25:21,408 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10313885910047 HTTP/1.1" 201 851
2025-05-16 02:25:21,408 - __main__ - ERROR - 尝试 3: 服务器玩家列表请求失败，状态码: 201
2025-05-16 02:25:21,409 - __main__ - ERROR - 服务器玩家列表所有重试失败，无法获取数据。
2025-05-16 02:25:21,409 - __main__ - WARNING - check_nuanfu_status for s1: async_get_players_gametools 返回无效数据。
2025-05-16 02:25:21,409 - __main__ - ERROR - [s1] 检查暖服状态失败: 无法获取有效的玩家数据。 - 监控将继续每分钟重试
2025-05-16 02:25:21,857 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 02:26:21,857 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 02:26:21,858 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 02:26:22,973 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10313885910047 HTTP/1.1" 201 851
2025-05-16 02:26:22,974 - __main__ - ERROR - 尝试 1: 服务器玩家列表请求失败，状态码: 201
2025-05-16 02:26:22,974 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 02:26:24,975 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 02:26:25,821 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10313885910047 HTTP/1.1" 201 851
2025-05-16 02:26:25,821 - __main__ - ERROR - 尝试 2: 服务器玩家列表请求失败，状态码: 201
2025-05-16 02:26:25,822 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 02:26:27,822 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 02:26:28,826 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10313885910047 HTTP/1.1" 201 851
2025-05-16 02:26:28,827 - __main__ - ERROR - 尝试 3: 服务器玩家列表请求失败，状态码: 201
2025-05-16 02:26:28,827 - __main__ - ERROR - 服务器玩家列表所有重试失败，无法获取数据。
2025-05-16 02:26:28,827 - __main__ - WARNING - check_nuanfu_status for s1: async_get_players_gametools 返回无效数据。
2025-05-16 02:26:28,827 - __main__ - ERROR - [s1] 检查暖服状态失败: 无法获取有效的玩家数据。 - 监控将继续每分钟重试
2025-05-16 02:26:29,229 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 02:27:07,734 - __main__ - INFO - NiuNiuBot配置加载成功
2025-05-16 02:27:07,734 - __main__ - INFO - 当前的NiuNiuBot配置:
Key               | Value
------------------------
address           | http://127.0.0.1:3000
token             | shikanoko
server_group_name | 2636
qq_bot            | 3989549945
admin_qq          | 350362423
check_admin       | 2720216977
active_groups     | [976420087]
public_groups     | 978880814
nfj_group         | 1037749375
nfj_bot           | 2720216977
ban_group         | 559046176
nfj_stop          | False
nfj_on            | True
our_server        | {'s1': '10313885910047', 's2': '10312665510199', 's3': '10269676270097', 's4': '10295688560381', 's5': '10306070430418', 'touhou': '10298171410685'}
nfj_server        | {'s1': '10307748850667', 's2': '10294976540859', 's3': '10269676270097', 's4': '10295688560381', 'touhou': '10298171410685'}
reminder          | True
reminder_groups   | [976420087]
reminder_set      | 
ai_judgement      | https://bfvai.yesu.eu.org/shikanoko/ai_judgment

2025-05-16 02:27:07,735 - __main__ - INFO - 使用绑定地址: 127.0.0.1, 端口: 18889
2025-05-16 02:27:07,735 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-16 02:27:07,735 - __main__ - INFO - 等待 asyncio 事件循环启动...
2025-05-16 02:27:07,736 - __main__ - INFO - Asyncio 事件循环线程启动。
2025-05-16 02:27:07,736 - __main__ - INFO - 主 asyncio 任务启动。
2025-05-16 02:27:07,736 - __main__ - INFO - 提醒循环任务已在主循环中启动。
2025-05-16 02:27:07,736 - __main__ - INFO - 动态命令 's1' 注册成功
2025-05-16 02:27:07,736 - __main__ - INFO - 提醒任务启动。
2025-05-16 02:27:07,736 - __main__ - INFO - 动态命令 's2' 注册成功
2025-05-16 02:27:07,736 - __main__ - INFO - 动态命令 's3' 注册成功
2025-05-16 02:27:07,737 - __main__ - INFO - 动态命令 's4' 注册成功
2025-05-16 02:27:07,737 - __main__ - INFO - 动态命令 's5' 注册成功
2025-05-16 02:27:07,737 - __main__ - INFO - 动态命令 'touhou' 注册成功
2025-05-16 02:27:07,737 - __main__ - INFO - 正在启动 Flask web 服务...
2025-05-16 02:27:12,220 - __main__ - INFO - 接收到命令: s1, 参数: , from user: 2720216977, group: 976420087
2025-05-16 02:27:12,267 - __main__ - ERROR - game_id 请求API失败: Cannot connect to host api.bfvrobot.net:443 ssl:default [指定的网络名不再可用。]
Traceback (most recent call last):
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 461, in finish_socket_func
    return ov.getresult()
           ^^^^^^^^^^^^^^
OSError: [WinError 64] 指定的网络名不再可用。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1122, in _wrap_create_connection
    return await self._loop.create_connection(*args, **kwargs, sock=sock)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\base_events.py", line 1126, in create_connection
    transport, protocol = await self._create_connection_transport(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\base_events.py", line 1159, in _create_connection_transport
    await waiter
  File "D:\project\python312\Lib\asyncio\proactor_events.py", line 286, in _loop_reading
    length = fut.result()
             ^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 798, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 465, in finish_socket_func
    raise ConnectionResetError(*exc.args)
ConnectionResetError: [WinError 64] 指定的网络名不再可用。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\bfv_ai_bot\niuniubot.py", line 59, in update_game_id
    async with session.get(url) as response:
  File "D:\project\python312\Lib\site-packages\aiohttp\client.py", line 1425, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\client.py", line 703, in _request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 548, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1056, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1400, in _create_direct_connection
    raise last_exc
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1369, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1130, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.bfvrobot.net:443 ssl:default [指定的网络名不再可用。]
2025-05-16 02:27:12,270 - __main__ - ERROR - reminder_set 配置无效，使用默认提醒时间。
2025-05-16 02:27:12,270 - __main__ - INFO - 下一次提醒时间: 2025-05-16 07:00:00 (等待 16367 秒)
2025-05-16 02:27:22,444 - __main__ - INFO - Flask web 服务已停止。
2025-05-16 02:28:37,285 - __main__ - INFO - NiuNiuBot配置加载成功
2025-05-16 02:28:37,285 - __main__ - INFO - 当前的NiuNiuBot配置:
Key               | Value
------------------------
address           | http://127.0.0.1:3000
token             | shikanoko
server_group_name | 2636
qq_bot            | 3989549945
admin_qq          | 350362423
check_admin       | 2720216977
active_groups     | [976420087]
public_groups     | 978880814
nfj_group         | 1037749375
nfj_bot           | 2720216977
ban_group         | 559046176
nfj_stop          | False
nfj_on            | True
our_server        | {'s1': '10313885910047', 's2': '10312665510199', 's3': '10269676270097', 's4': '10295688560381', 's5': '10306070430418', 'touhou': '10298171410685'}
nfj_server        | {'s1': '10307748850667', 's2': '10294976540859', 's3': '10269676270097', 's4': '10295688560381', 'touhou': '10298171410685'}
reminder          | True
reminder_groups   | [976420087]
reminder_set      | 
ai_judgement      | https://bfvai.yesu.eu.org/shikanoko/ai_judgment

2025-05-16 02:28:37,286 - __main__ - INFO - 使用绑定地址: 127.0.0.1, 端口: 18889
2025-05-16 02:28:37,286 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-16 02:28:37,286 - __main__ - INFO - 等待 asyncio 事件循环启动...
2025-05-16 02:28:37,287 - __main__ - INFO - Asyncio 事件循环线程启动。
2025-05-16 02:28:37,287 - __main__ - INFO - 主 asyncio 任务启动。
2025-05-16 02:28:37,287 - __main__ - INFO - 提醒循环任务已在主循环中启动。
2025-05-16 02:28:37,287 - __main__ - INFO - 动态命令 's1' 注册成功
2025-05-16 02:28:37,287 - __main__ - INFO - 提醒任务启动。
2025-05-16 02:28:37,287 - __main__ - INFO - 动态命令 's2' 注册成功
2025-05-16 02:28:37,288 - __main__ - INFO - 动态命令 's3' 注册成功
2025-05-16 02:28:37,288 - __main__ - INFO - 动态命令 's4' 注册成功
2025-05-16 02:28:37,288 - __main__ - INFO - 动态命令 's5' 注册成功
2025-05-16 02:28:37,288 - __main__ - INFO - 动态命令 'touhou' 注册成功
2025-05-16 02:28:37,289 - __main__ - INFO - 正在启动 Flask web 服务...
2025-05-16 02:28:39,204 - __main__ - INFO - config.json 加载成功
2025-05-16 02:28:39,204 - __main__ - INFO - s1 的 gameId 未变化: 10313885910047
2025-05-16 02:28:39,204 - __main__ - INFO - s2 的 gameId 未变化: 10312665510199
2025-05-16 02:28:39,204 - __main__ - INFO - s3 的 gameId 未变化: 10269676270097
2025-05-16 02:28:39,204 - __main__ - INFO - s4 的 gameId 未变化: 10295688560381
2025-05-16 02:28:39,204 - __main__ - INFO - s5 的 gameId 未变化: 10306070430418
2025-05-16 02:28:39,204 - __main__ - WARNING - 未找到匹配的服务器: touhou
2025-05-16 02:28:39,204 - __main__ - INFO - NiuNiuBot 配置重新加载成功
2025-05-16 02:28:39,205 - __main__ - ERROR - reminder_set 配置无效，使用默认提醒时间。
2025-05-16 02:28:39,205 - __main__ - INFO - 下一次提醒时间: 2025-05-16 07:00:00 (等待 16280 秒)
2025-05-16 02:28:46,181 - __main__ - INFO - 接收到命令: s1, 参数: , from user: 2720216977, group: 976420087
2025-05-16 02:29:07,130 - __main__ - INFO - Flask web 服务已停止。
2025-05-16 02:49:02,203 - __main__ - INFO - NiuNiuBot配置加载成功
2025-05-16 02:49:02,203 - __main__ - INFO - 当前的NiuNiuBot配置:
Key               | Value
------------------------
address           | http://127.0.0.1:3000
token             | shikanoko
server_group_name | 2636
qq_bot            | 3989549945
admin_qq          | 350362423
check_admin       | 2720216977
active_groups     | [982726092]
public_groups     | 982726092
nfj_group         | 976420087
nfj_bot           | 2720216977
ban_group         | 559046176
nfj_stop          | False
nfj_on            | True
our_server        | {'s1': '10313885910047', 's2': '10312665510199', 's3': '10269676270097', 's4': '10295688560381', 's5': '10306070430418'}
nfj_server        | {'s1': '10307748850667', 's2': '10294976540859', 's3': '10269676270097', 's4': '10295688560381'}
reminder          | True
reminder_groups   | [976420087]
reminder_set      | 
ai_judgement      | https://bfvai.yesu.eu.org/shikanoko/ai_judgment

2025-05-16 02:49:02,205 - __main__ - INFO - 使用绑定地址: 127.0.0.1, 端口: 18889
2025-05-16 02:49:02,206 - __main__ - INFO - 等待 asyncio 事件循环启动...
2025-05-16 02:49:02,206 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-16 02:49:02,207 - __main__ - INFO - Asyncio 事件循环线程启动。
2025-05-16 02:49:02,207 - __main__ - INFO - 主 asyncio 任务启动。
2025-05-16 02:49:02,207 - __main__ - INFO - 提醒循环任务已在主循环中启动。
2025-05-16 02:49:02,207 - __main__ - INFO - 动态命令 's1' 注册成功
2025-05-16 02:49:02,208 - __main__ - INFO - 提醒任务启动。
2025-05-16 02:49:02,208 - __main__ - INFO - 动态命令 's2' 注册成功
2025-05-16 02:49:02,208 - __main__ - INFO - 动态命令 's3' 注册成功
2025-05-16 02:49:02,209 - __main__ - INFO - 动态命令 's4' 注册成功
2025-05-16 02:49:02,209 - __main__ - INFO - 动态命令 's5' 注册成功
2025-05-16 02:49:02,210 - __main__ - INFO - 正在启动 Flask web 服务...
2025-05-16 02:49:04,751 - __main__ - ERROR - game_id 请求API失败: Cannot connect to host api.bfvrobot.net:443 ssl:default [指定的网络名不再可用。]
Traceback (most recent call last):
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 461, in finish_socket_func
    return ov.getresult()
           ^^^^^^^^^^^^^^
OSError: [WinError 64] 指定的网络名不再可用。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1122, in _wrap_create_connection
    return await self._loop.create_connection(*args, **kwargs, sock=sock)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\base_events.py", line 1126, in create_connection
    transport, protocol = await self._create_connection_transport(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\base_events.py", line 1159, in _create_connection_transport
    await waiter
  File "D:\project\python312\Lib\asyncio\proactor_events.py", line 286, in _loop_reading
    length = fut.result()
             ^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 798, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 465, in finish_socket_func
    raise ConnectionResetError(*exc.args)
ConnectionResetError: [WinError 64] 指定的网络名不再可用。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\bfv_ai_bot\niuniubot.py", line 59, in update_game_id
    async with session.get(url) as response:
  File "D:\project\python312\Lib\site-packages\aiohttp\client.py", line 1425, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\client.py", line 703, in _request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 548, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1056, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1400, in _create_direct_connection
    raise last_exc
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1369, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1130, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.bfvrobot.net:443 ssl:default [指定的网络名不再可用。]
2025-05-16 02:49:04,753 - __main__ - ERROR - reminder_set 配置无效，使用默认提醒时间。
2025-05-16 02:49:04,754 - __main__ - INFO - 下一次提醒时间: 2025-05-16 07:00:00 (等待 15055 秒)
2025-05-16 02:49:22,088 - __main__ - INFO - 接收到命令: s1, 参数: , from user: 2720216977, group: 982726092
2025-05-16 02:49:34,164 - __main__ - INFO - 接收到命令: nfj, 参数: s1, from user: 2720216977, group: 982726092
2025-05-16 02:49:35,020 - __main__ - INFO - check_rsp: 第 1 次尝试未找到响应消息 (msg_id 6438657)，等待3秒后重试...
2025-05-16 02:49:38,107 - __main__ - INFO - check_rsp: 第 2 次尝试未找到响应消息 (msg_id 6438657)，等待3秒后重试...
2025-05-16 02:49:41,147 - __main__ - INFO - check_rsp: 第 3 次尝试未找到响应消息 (msg_id 6438657)，等待3秒后重试...
2025-05-16 02:49:44,205 - __main__ - INFO - check_rsp: 第 4 次尝试未找到响应消息 (msg_id 6438657)，等待3秒后重试...
2025-05-16 02:49:47,252 - __main__ - INFO - check_rsp: 第 5 次尝试未找到响应消息 (msg_id 6438657)，等待3秒后重试...
2025-05-16 02:49:50,282 - __main__ - INFO - check_rsp: 第 6 次尝试未找到响应消息 (msg_id 6438657)，等待3秒后重试...
2025-05-16 02:49:53,334 - __main__ - INFO - check_rsp: 找到并解析第一个响应消息: message_id 1206776763
2025-05-16 02:49:53,836 - __main__ - INFO - 已成功转发 /warm 2636 s1 命令结果到群组 982726092
2025-05-16 02:50:41,313 - __main__ - INFO - 接收到命令: nfb, 参数: , from user: 2720216977, group: 982726092
2025-05-16 02:50:42,382 - __main__ - INFO - check_rsp: 第 1 次尝试未找到响应消息 (msg_id 1753130355)，等待3秒后重试...
2025-05-16 02:50:45,432 - __main__ - INFO - check_rsp: 第 2 次尝试未找到响应消息 (msg_id 1753130355)，等待3秒后重试...
2025-05-16 02:50:48,498 - __main__ - INFO - check_rsp: 找到并解析第一个响应消息: message_id 1761612003
2025-05-16 02:50:49,104 - __main__ - INFO - 已成功转发 /nfb 命令结果到群组 982726092
2025-05-16 02:53:05,142 - __main__ - INFO - Flask web 服务已停止。
2025-05-16 02:53:06,574 - __main__ - INFO - NiuNiuBot配置加载成功
2025-05-16 02:53:06,575 - __main__ - INFO - 当前的NiuNiuBot配置:
Key               | Value
------------------------
address           | http://127.0.0.1:3000
token             | shikanoko
server_group_name | 2636
qq_bot            | 3989549945
admin_qq          | 350362423
check_admin       | 2720216977
active_groups     | [982726092]
public_groups     | 982726092
nfj_group         | 976420087
nfj_bot           | 2720216977
ban_group         | 559046176
nfj_stop          | False
nfj_on            | True
our_server        | {'s1': '10313885910047', 's2': '10312665510199', 's3': '10269676270097', 's4': '10295688560381', 's5': '10306070430418'}
nfj_server        | {'s1': '10307748850667', 's2': '10294976540859', 's3': '10269676270097', 's4': '10295688560381'}
reminder          | True
reminder_groups   | [976420087]
reminder_set      | 
ai_judgement      | https://bfvai.yesu.eu.org/shikanoko/ai_judgment

2025-05-16 02:53:06,575 - __main__ - INFO - 使用绑定地址: 127.0.0.1, 端口: 18889
2025-05-16 02:53:06,576 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-16 02:53:06,576 - __main__ - INFO - 等待 asyncio 事件循环启动...
2025-05-16 02:53:06,577 - __main__ - INFO - Asyncio 事件循环线程启动。
2025-05-16 02:53:06,577 - __main__ - INFO - 主 asyncio 任务启动。
2025-05-16 02:53:06,578 - __main__ - INFO - 提醒循环任务已在主循环中启动。
2025-05-16 02:53:06,578 - __main__ - INFO - 动态命令 's1' 注册成功
2025-05-16 02:53:06,578 - __main__ - INFO - 提醒任务启动。
2025-05-16 02:53:06,578 - __main__ - INFO - 动态命令 's2' 注册成功
2025-05-16 02:53:06,579 - __main__ - INFO - 动态命令 's3' 注册成功
2025-05-16 02:53:06,580 - __main__ - INFO - 动态命令 's4' 注册成功
2025-05-16 02:53:06,580 - __main__ - INFO - 动态命令 's5' 注册成功
2025-05-16 02:53:06,580 - __main__ - INFO - 正在启动 Flask web 服务...
2025-05-16 02:53:09,515 - __main__ - INFO - config.json 加载成功
2025-05-16 02:53:09,516 - __main__ - INFO - s1 的 gameId 未变化: 10313885910047
2025-05-16 02:53:09,516 - __main__ - INFO - s2 的 gameId 未变化: 10312665510199
2025-05-16 02:53:09,516 - __main__ - INFO - s3 的 gameId 未变化: 10269676270097
2025-05-16 02:53:09,516 - __main__ - INFO - s4 的 gameId 未变化: 10295688560381
2025-05-16 02:53:09,516 - __main__ - INFO - s5 的 gameId 未变化: 10306070430418
2025-05-16 02:53:09,516 - __main__ - INFO - NiuNiuBot 配置重新加载成功
2025-05-16 02:53:09,516 - __main__ - ERROR - reminder_set 配置无效，使用默认提醒时间。
2025-05-16 02:53:09,517 - __main__ - INFO - 下一次提醒时间: 2025-05-16 07:00:00 (等待 14810 秒)
2025-05-16 02:53:13,505 - __main__ - INFO - 接收到命令: nfb, 参数: , from user: 2720216977, group: 982726092
2025-05-16 02:53:14,440 - __main__ - INFO - check_rsp: 第 1 次尝试未找到响应消息 (msg_id 813400302)，等待3秒后重试...
2025-05-16 02:53:17,459 - __main__ - INFO - check_rsp: 找到并解析第一个响应消息: message_id 202891552
2025-05-16 02:53:18,032 - __main__ - INFO - 已成功转发 /nfb 命令结果到群组 982726092
2025-05-16 02:53:47,490 - __main__ - INFO - 接收到命令: nfj, 参数: s4, from user: 2720216977, group: 982726092
2025-05-16 02:53:48,459 - __main__ - INFO - check_rsp: 第 1 次尝试未找到响应消息 (msg_id 647457882)，等待3秒后重试...
2025-05-16 02:53:51,528 - __main__ - INFO - check_rsp: 找到并解析第一个响应消息: message_id 1757324745
2025-05-16 02:53:52,111 - __main__ - INFO - 已成功转发 /warm 2636 s4 命令结果到群组 982726092
2025-05-16 02:54:15,410 - __main__ - INFO - 接收到命令: 2, 参数: , from user: 2720216977, group: 982726092
2025-05-16 02:54:46,943 - __main__ - INFO - 接收到命令: zc, 参数: s2, from user: 2720216977, group: 982726092
2025-05-16 02:54:47,467 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 02:54:48,864 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10312665510199 HTTP/1.1" 201 869
2025-05-16 02:54:48,865 - __main__ - ERROR - 尝试 1: 服务器玩家列表请求失败，状态码: 201
2025-05-16 02:54:48,865 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 02:54:50,867 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 02:54:51,983 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10312665510199 HTTP/1.1" 201 869
2025-05-16 02:54:51,984 - __main__ - ERROR - 尝试 2: 服务器玩家列表请求失败，状态码: 201
2025-05-16 02:54:51,984 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 02:54:53,986 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 02:54:54,868 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10312665510199 HTTP/1.1" 201 869
2025-05-16 02:54:54,869 - __main__ - ERROR - 尝试 3: 服务器玩家列表请求失败，状态码: 201
2025-05-16 02:54:54,870 - __main__ - ERROR - 服务器玩家列表所有重试失败，无法获取数据。
2025-05-16 02:54:54,870 - __main__ - WARNING - cmd_zc: 无法获取服务器 s2 的玩家数据，AI判断将不含玩家列表。
2025-05-16 02:54:54,870 - __main__ - INFO - cmd_zc: 检测到回复消息，目标消息ID: 1272413699
2025-05-16 02:54:54,882 - __main__ - INFO - cmd_zc: 正在调用AI处理图片 https://multimedia.nt.qq.com.cn/download?appid=1407&fileid=EhTE0wBMJqLLmO8zcc5OXYLkUU42TRjszoMBIP8KKLrBw5STpo0DMgRwcm9kUIC9owFaEO-eMx8K_o1xS3-fsjkwFbZ6Ajof&rkey=CAMSMBmBGMV3MGQFrnn7PdCLXOdVPMyoD8S04pf4ZMo0gpwwtSoc6_dPB1onmWvoFmAmyQ...
2025-05-16 02:55:02,908 - __main__ - INFO - AI判断响应状态码: 200
2025-05-16 02:55:02,908 - __main__ - INFO - cmd_zc: AI处理结束，结果类型: <class 'dict'>
2025-05-16 02:55:02,909 - __main__ - DEBUG - cmd_zc: AI原始结果: {'judgment': '1', 'player': 'hcr20080111', 'chat': 'Ava038:点没掉快乐洞的坦克,别打狙\nhcr20080111:ΤΑΝ KE?\nhcr20080111: TANKE LAI A\nAR-L 15vo: PEN HUO XIE ER MAN NE?\nhcr20080111:TAN KE SI MA L\nhcr20080111:LIA SI MA ΖΑΙ'}
2025-05-16 02:55:02,909 - __main__ - INFO - cmd_zc: AI检测到语言攻击。玩家: hcr20080111, 对话: Ava038:点没掉快乐洞的坦克,别打狙
hcr20080111:ΤΑΝ KE?
hcr20080111: TANKE LAI A
AR-L 15vo: PEN HUO XIE ER MAN NE?
hcr20080111:TAN KE SI MA L
hcr20080111:LIA SI MA ΖΑΙ
2025-05-16 02:55:03,908 - __main__ - INFO - cmd_zc: 已通知管理员 2720216977 关于语言攻击举报。
2025-05-16 02:58:54,831 - __main__ - INFO - Flask web 服务已停止。
2025-05-16 02:58:59,124 - __main__ - INFO - NiuNiuBot配置加载成功
2025-05-16 02:58:59,125 - __main__ - INFO - 当前的NiuNiuBot配置:
Key               | Value
------------------------
address           | http://127.0.0.1:3000
token             | shikanoko
server_group_name | 2636
qq_bot            | 3989549945
admin_qq          | 350362423
check_admin       | 2720216977
active_groups     | [982726092]
public_groups     | 982726092
nfj_group         | 976420087
nfj_bot           | 2720216977
ban_group         | 559046176
nfj_stop          | False
nfj_on            | True
our_server        | {'s1': '10313885910047', 's2': '10312665510199', 's3': '10269676270097', 's4': '10295688560381', 's5': '10306070430418'}
nfj_server        | {'s1': '10307748850667', 's2': '10294976540859', 's3': '10269676270097', 's4': '10295688560381'}
reminder          | True
reminder_groups   | [976420087]
reminder_set      | 
ai_judgement      | https://bfvai.yesu.eu.org/shikanoko/ai_judgment

2025-05-16 02:58:59,125 - __main__ - INFO - 使用绑定地址: 127.0.0.1, 端口: 18889
2025-05-16 02:58:59,126 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-16 02:58:59,126 - __main__ - INFO - 等待 asyncio 事件循环启动...
2025-05-16 02:58:59,127 - __main__ - INFO - Asyncio 事件循环线程启动。
2025-05-16 02:58:59,127 - __main__ - INFO - 主 asyncio 任务启动。
2025-05-16 02:58:59,127 - __main__ - INFO - 提醒循环任务已在主循环中启动。
2025-05-16 02:58:59,127 - __main__ - INFO - 动态命令 's1' 注册成功
2025-05-16 02:58:59,128 - __main__ - INFO - 提醒任务启动。
2025-05-16 02:58:59,128 - __main__ - INFO - 动态命令 's2' 注册成功
2025-05-16 02:58:59,128 - __main__ - INFO - 动态命令 's3' 注册成功
2025-05-16 02:58:59,129 - __main__ - INFO - 动态命令 's4' 注册成功
2025-05-16 02:58:59,129 - __main__ - INFO - 动态命令 's5' 注册成功
2025-05-16 02:58:59,129 - __main__ - INFO - 正在启动 Flask web 服务...
2025-05-16 02:59:01,377 - __main__ - INFO - config.json 加载成功
2025-05-16 02:59:01,378 - __main__ - INFO - s1 的 gameId 未变化: 10313885910047
2025-05-16 02:59:01,378 - __main__ - INFO - s2 的 gameId 未变化: 10312665510199
2025-05-16 02:59:01,378 - __main__ - INFO - s3 的 gameId 未变化: 10269676270097
2025-05-16 02:59:01,378 - __main__ - INFO - s4 的 gameId 未变化: 10295688560381
2025-05-16 02:59:01,378 - __main__ - INFO - s5 的 gameId 未变化: 10306070430418
2025-05-16 02:59:01,378 - __main__ - INFO - NiuNiuBot 配置重新加载成功
2025-05-16 02:59:01,378 - __main__ - ERROR - reminder_set 配置无效，使用默认提醒时间。
2025-05-16 02:59:01,378 - __main__ - INFO - 下一次提醒时间: 2025-05-16 07:00:00 (等待 14458 秒)
2025-05-16 02:59:04,656 - __main__ - INFO - 接收到命令: zc, 参数: s2, from user: 2720216977, group: 982726092
2025-05-16 02:59:05,147 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 02:59:06,524 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10312665510199 HTTP/1.1" 201 869
2025-05-16 02:59:06,525 - __main__ - ERROR - 尝试 1: 服务器玩家列表请求失败，状态码: 201
2025-05-16 02:59:06,525 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 02:59:08,526 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 02:59:09,402 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10312665510199 HTTP/1.1" 201 869
2025-05-16 02:59:09,403 - __main__ - ERROR - 尝试 2: 服务器玩家列表请求失败，状态码: 201
2025-05-16 02:59:09,403 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 02:59:11,404 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 02:59:12,822 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10312665510199 HTTP/1.1" 201 869
2025-05-16 02:59:12,823 - __main__ - ERROR - 尝试 3: 服务器玩家列表请求失败，状态码: 201
2025-05-16 02:59:12,823 - __main__ - ERROR - 服务器玩家列表所有重试失败，无法获取数据。
2025-05-16 02:59:12,825 - __main__ - WARNING - cmd_zc: 无法获取服务器 s2 的玩家数据，AI判断将不含玩家列表。
2025-05-16 02:59:12,826 - __main__ - INFO - cmd_zc: 检测到回复消息，目标消息ID: 1272413699
2025-05-16 02:59:12,839 - __main__ - INFO - cmd_zc: 正在调用AI处理图片 https://multimedia.nt.qq.com.cn/download?appid=1407&fileid=EhTE0wBMJqLLmO8zcc5OXYLkUU42TRjszoMBIP8KKLrBw5STpo0DMgRwcm9kUIC9owFaEO-eMx8K_o1xS3-fsjkwFbZ6Ajof&rkey=CAMSMBmBGMV3MGQFrnn7PdCLXOdVPMyoD8S04pf4ZMo0gpwwtSoc6_dPB1onmWvoFmAmyQ...
2025-05-16 02:59:20,096 - __main__ - INFO - AI判断响应状态码: 200
2025-05-16 02:59:20,097 - __main__ - INFO - cmd_zc: AI处理结束，结果类型: <class 'dict'>
2025-05-16 02:59:20,097 - __main__ - DEBUG - cmd_zc: AI原始结果: {'judgment': '1', 'player': 'hcr20080111', 'chat': 'Ava038:点没掉快乐洞的坦克,别打狙\nhcr20080111: TAN KE?\nhcr20080111: TANKE LAI A\nAR-L 15vo: PEN HUO XIE ER MAN NE?\nhcr20080111:TAN KE SI MA L\nhcr20080111:LIA SI MA ZAI'}
2025-05-16 02:59:20,097 - __main__ - INFO - cmd_zc: AI检测到语言攻击。玩家: hcr20080111, 对话: Ava038:点没掉快乐洞的坦克,别打狙
hcr20080111: TAN KE?
hcr20080111: TANKE LAI A
AR-L 15vo: PEN HUO XIE ER MAN NE?
hcr20080111:TAN KE SI MA L
hcr20080111:LIA SI MA ZAI
2025-05-16 02:59:21,225 - __main__ - INFO - cmd_zc: 已通知管理员 2720216977 关于语言攻击举报。
2025-05-16 03:01:03,329 - __main__ - INFO - Flask web 服务已停止。
2025-05-16 03:01:05,149 - __main__ - INFO - NiuNiuBot配置加载成功
2025-05-16 03:01:05,150 - __main__ - INFO - 当前的NiuNiuBot配置:
Key               | Value
------------------------
address           | http://127.0.0.1:3000
token             | shikanoko
server_group_name | 2636
qq_bot            | 3989549945
admin_qq          | 350362423
check_admin       | 2720216977
active_groups     | [982726092]
public_groups     | 982726092
nfj_group         | 976420087
nfj_bot           | 2720216977
ban_group         | 559046176
nfj_stop          | False
nfj_on            | True
our_server        | {'s1': '10313885910047', 's2': '10312665510199', 's3': '10269676270097', 's4': '10295688560381', 's5': '10306070430418'}
nfj_server        | {'s1': '10307748850667', 's2': '10294976540859', 's3': '10269676270097', 's4': '10295688560381'}
reminder          | True
reminder_groups   | [976420087]
reminder_set      | 
ai_judgement      | https://bfvai.yesu.eu.org/shikanoko/ai_judgment

2025-05-16 03:01:05,150 - __main__ - INFO - 使用绑定地址: 127.0.0.1, 端口: 18889
2025-05-16 03:01:05,151 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-16 03:01:05,151 - __main__ - INFO - 等待 asyncio 事件循环启动...
2025-05-16 03:01:05,152 - __main__ - INFO - Asyncio 事件循环线程启动。
2025-05-16 03:01:05,152 - __main__ - INFO - 主 asyncio 任务启动。
2025-05-16 03:01:05,152 - __main__ - INFO - 提醒循环任务已在主循环中启动。
2025-05-16 03:01:05,152 - __main__ - INFO - 动态命令 's1' 注册成功
2025-05-16 03:01:05,152 - __main__ - INFO - 提醒任务启动。
2025-05-16 03:01:05,152 - __main__ - INFO - 动态命令 's2' 注册成功
2025-05-16 03:01:05,153 - __main__ - INFO - 动态命令 's3' 注册成功
2025-05-16 03:01:05,153 - __main__ - INFO - 动态命令 's4' 注册成功
2025-05-16 03:01:05,153 - __main__ - INFO - 动态命令 's5' 注册成功
2025-05-16 03:01:05,154 - __main__ - INFO - 正在启动 Flask web 服务...
2025-05-16 03:01:08,168 - __main__ - INFO - config.json 加载成功
2025-05-16 03:01:08,168 - __main__ - INFO - s1 的 gameId 未变化: 10313885910047
2025-05-16 03:01:08,168 - __main__ - INFO - s2 的 gameId 未变化: 10312665510199
2025-05-16 03:01:08,169 - __main__ - INFO - s3 的 gameId 未变化: 10269676270097
2025-05-16 03:01:08,169 - __main__ - INFO - s4 的 gameId 未变化: 10295688560381
2025-05-16 03:01:08,169 - __main__ - INFO - s5 的 gameId 未变化: 10306070430418
2025-05-16 03:01:08,169 - __main__ - INFO - NiuNiuBot 配置重新加载成功
2025-05-16 03:01:08,169 - __main__ - ERROR - reminder_set 配置无效，使用默认提醒时间。
2025-05-16 03:01:08,169 - __main__ - INFO - 下一次提醒时间: 2025-05-16 07:00:00 (等待 14331 秒)
2025-05-16 03:01:31,118 - __main__ - INFO - 接收到命令: zc, 参数: s2, from user: 2720216977, group: 982726092
2025-05-16 03:01:31,730 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 03:01:32,955 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10312665510199 HTTP/1.1" 201 869
2025-05-16 03:01:32,955 - __main__ - ERROR - 尝试 1: 服务器玩家列表请求失败，状态码: 201
2025-05-16 03:01:32,955 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 03:01:34,957 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 03:01:36,736 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10312665510199 HTTP/1.1" 201 869
2025-05-16 03:01:36,736 - __main__ - ERROR - 尝试 2: 服务器玩家列表请求失败，状态码: 201
2025-05-16 03:01:36,737 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 03:01:38,738 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 03:01:39,581 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10312665510199 HTTP/1.1" 201 869
2025-05-16 03:01:39,582 - __main__ - ERROR - 尝试 3: 服务器玩家列表请求失败，状态码: 201
2025-05-16 03:01:39,582 - __main__ - ERROR - 服务器玩家列表所有重试失败，无法获取数据。
2025-05-16 03:01:39,582 - __main__ - WARNING - cmd_zc: 无法获取服务器 s2 的玩家数据，AI判断将不含玩家列表。
2025-05-16 03:01:39,583 - __main__ - INFO - cmd_zc: 检测到回复消息，目标消息ID: 1093143652
2025-05-16 03:01:39,594 - __main__ - INFO - cmd_zc: 正在调用AI处理图片 https://multimedia.nt.qq.com.cn/download?appid=1407&fileid=EhTIk_WI1LxldA0toQ3DkyRCbQjbzBjxkAMg_wooo7ypypSmjQMyBHByb2RQgL2jAVoQfdHQe1RwW55UfslpZ0z0IXoC3mM&rkey=CAMSMBmBGMV3MGQFrnn7PdCLXOdVPMyoD8S04pf4ZMo0gpwwtSoc6_dPB1onmWvoFmAmyQ...
2025-05-16 03:01:44,042 - __main__ - INFO - AI判断响应状态码: 200
2025-05-16 03:01:44,043 - __main__ - INFO - cmd_zc: AI处理结束，结果类型: <class 'dict'>
2025-05-16 03:01:44,043 - __main__ - DEBUG - cmd_zc: AI原始结果: {'judgment': '1', 'player': 'Megumi_000000000', 'chat': 'TVBot: 玩家 hzwygl被管理员封禁,原因:超\n杀\nMegumi_000000000 EZ EZ EZ EZ\nyxh666688 gg\nahahahero:?\nbkinaciluk:GUO YIN\n聊天请按下ENTER'}
2025-05-16 03:01:44,043 - __main__ - INFO - cmd_zc: AI检测到语言攻击。玩家: Megumi_000000000, 对话: TVBot: 玩家 hzwygl被管理员封禁,原因:超
杀
Megumi_000000000 EZ EZ EZ EZ
yxh666688 gg
ahahahero:?
bkinaciluk:GUO YIN
聊天请按下ENTER
2025-05-16 03:01:45,202 - __main__ - INFO - cmd_zc: 已通知管理员 2720216977 关于语言攻击举报。
2025-05-16 03:03:15,940 - __main__ - INFO - Flask web 服务已停止。
2025-05-16 03:03:17,467 - __main__ - INFO - NiuNiuBot配置加载成功
2025-05-16 03:03:17,468 - __main__ - INFO - 当前的NiuNiuBot配置:
Key               | Value
------------------------
address           | http://127.0.0.1:3000
token             | shikanoko
server_group_name | 2636
qq_bot            | 3989549945
admin_qq          | 350362423
check_admin       | 2720216977
active_groups     | [982726092]
public_groups     | 982726092
nfj_group         | 976420087
nfj_bot           | 2720216977
ban_group         | 559046176
nfj_stop          | False
nfj_on            | True
our_server        | {'s1': '10313885910047', 's2': '10312665510199', 's3': '10269676270097', 's4': '10295688560381', 's5': '10306070430418'}
nfj_server        | {'s1': '10307748850667', 's2': '10294976540859', 's3': '10269676270097', 's4': '10295688560381'}
reminder          | True
reminder_groups   | [976420087]
reminder_set      | 
ai_judgement      | https://bfvai.yesu.eu.org/shikanoko/ai_judgment

2025-05-16 03:03:17,469 - __main__ - INFO - 使用绑定地址: 127.0.0.1, 端口: 18889
2025-05-16 03:03:17,469 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-16 03:03:17,469 - __main__ - INFO - 等待 asyncio 事件循环启动...
2025-05-16 03:03:17,470 - __main__ - INFO - Asyncio 事件循环线程启动。
2025-05-16 03:03:17,470 - __main__ - INFO - 主 asyncio 任务启动。
2025-05-16 03:03:17,470 - __main__ - INFO - 提醒循环任务已在主循环中启动。
2025-05-16 03:03:17,470 - __main__ - INFO - 动态命令 's1' 注册成功
2025-05-16 03:03:17,471 - __main__ - INFO - 提醒任务启动。
2025-05-16 03:03:17,471 - __main__ - INFO - 动态命令 's2' 注册成功
2025-05-16 03:03:17,471 - __main__ - INFO - 动态命令 's3' 注册成功
2025-05-16 03:03:17,472 - __main__ - INFO - 动态命令 's4' 注册成功
2025-05-16 03:03:17,472 - __main__ - INFO - 动态命令 's5' 注册成功
2025-05-16 03:03:17,472 - __main__ - INFO - 正在启动 Flask web 服务...
2025-05-16 03:03:20,613 - __main__ - ERROR - game_id 请求API失败: Cannot connect to host api.bfvrobot.net:443 ssl:default [指定的网络名不再可用。]
Traceback (most recent call last):
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 461, in finish_socket_func
    return ov.getresult()
           ^^^^^^^^^^^^^^
OSError: [WinError 64] 指定的网络名不再可用。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1122, in _wrap_create_connection
    return await self._loop.create_connection(*args, **kwargs, sock=sock)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\base_events.py", line 1126, in create_connection
    transport, protocol = await self._create_connection_transport(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\base_events.py", line 1159, in _create_connection_transport
    await waiter
  File "D:\project\python312\Lib\asyncio\proactor_events.py", line 286, in _loop_reading
    length = fut.result()
             ^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 798, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 465, in finish_socket_func
    raise ConnectionResetError(*exc.args)
ConnectionResetError: [WinError 64] 指定的网络名不再可用。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\bfv_ai_bot\niuniubot.py", line 59, in update_game_id
    async with session.get(url) as response:
  File "D:\project\python312\Lib\site-packages\aiohttp\client.py", line 1425, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\client.py", line 703, in _request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 548, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1056, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1400, in _create_direct_connection
    raise last_exc
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1369, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1130, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.bfvrobot.net:443 ssl:default [指定的网络名不再可用。]
2025-05-16 03:03:20,615 - __main__ - ERROR - reminder_set 配置无效，使用默认提醒时间。
2025-05-16 03:03:20,615 - __main__ - INFO - 下一次提醒时间: 2025-05-16 07:01:00 (等待 14259 秒)
2025-05-16 03:03:47,858 - __main__ - INFO - NiuNiuBot配置加载成功
2025-05-16 03:03:47,858 - __main__ - INFO - 当前的NiuNiuBot配置:
Key               | Value
------------------------
address           | http://127.0.0.1:3000
token             | shikanoko
server_group_name | 2636
qq_bot            | 3989549945
admin_qq          | 350362423
check_admin       | 2720216977
active_groups     | [982726092]
public_groups     | 982726092
nfj_group         | 976420087
nfj_bot           | 2720216977
ban_group         | 559046176
nfj_stop          | False
nfj_on            | True
our_server        | {'s1': '10313885910047', 's2': '10312665510199', 's3': '10269676270097', 's4': '10295688560381', 's5': '10306070430418'}
nfj_server        | {'s1': '10307748850667', 's2': '10294976540859', 's3': '10269676270097', 's4': '10295688560381'}
reminder          | True
reminder_groups   | [976420087]
reminder_set      | 
ai_judgement      | https://bfvai.yesu.eu.org/shikanoko/ai_judgment

2025-05-16 03:03:47,860 - __main__ - INFO - 使用绑定地址: 127.0.0.1, 端口: 18889
2025-05-16 03:03:47,860 - __main__ - INFO - 等待 asyncio 事件循环启动...
2025-05-16 03:03:47,860 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-16 03:03:47,862 - __main__ - INFO - Asyncio 事件循环线程启动。
2025-05-16 03:03:47,862 - __main__ - INFO - 主 asyncio 任务启动。
2025-05-16 03:03:47,863 - __main__ - INFO - 提醒循环任务已在主循环中启动。
2025-05-16 03:03:47,863 - __main__ - INFO - 动态命令 's1' 注册成功
2025-05-16 03:03:47,863 - __main__ - INFO - 提醒任务启动。
2025-05-16 03:03:47,863 - __main__ - INFO - 动态命令 's2' 注册成功
2025-05-16 03:03:47,865 - __main__ - INFO - 动态命令 's3' 注册成功
2025-05-16 03:03:47,866 - __main__ - INFO - 动态命令 's4' 注册成功
2025-05-16 03:03:47,866 - __main__ - INFO - 动态命令 's5' 注册成功
2025-05-16 03:03:47,867 - __main__ - INFO - 正在启动 Flask web 服务...
2025-05-16 03:03:50,358 - __main__ - ERROR - game_id 请求API失败: Cannot connect to host api.bfvrobot.net:443 ssl:default [指定的网络名不再可用。]
Traceback (most recent call last):
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 461, in finish_socket_func
    return ov.getresult()
           ^^^^^^^^^^^^^^
OSError: [WinError 64] 指定的网络名不再可用。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1122, in _wrap_create_connection
    return await self._loop.create_connection(*args, **kwargs, sock=sock)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\base_events.py", line 1126, in create_connection
    transport, protocol = await self._create_connection_transport(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\base_events.py", line 1159, in _create_connection_transport
    await waiter
  File "D:\project\python312\Lib\asyncio\proactor_events.py", line 286, in _loop_reading
    length = fut.result()
             ^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 798, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 465, in finish_socket_func
    raise ConnectionResetError(*exc.args)
ConnectionResetError: [WinError 64] 指定的网络名不再可用。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\bfv_ai_bot\niuniubot.py", line 59, in update_game_id
    async with session.get(url) as response:
  File "D:\project\python312\Lib\site-packages\aiohttp\client.py", line 1425, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\client.py", line 703, in _request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 548, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1056, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1400, in _create_direct_connection
    raise last_exc
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1369, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1130, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.bfvrobot.net:443 ssl:default [指定的网络名不再可用。]
2025-05-16 03:03:50,360 - __main__ - ERROR - reminder_set 配置无效，使用默认提醒时间。
2025-05-16 03:03:50,360 - __main__ - INFO - 下一次提醒时间: 2025-05-16 07:01:00 (等待 14229 秒)
2025-05-16 03:04:13,513 - __main__ - INFO - NiuNiuBot配置加载成功
2025-05-16 03:04:13,513 - __main__ - INFO - 当前的NiuNiuBot配置:
Key               | Value
------------------------
address           | http://127.0.0.1:3000
token             | shikanoko
server_group_name | 2636
qq_bot            | 3989549945
admin_qq          | 350362423
check_admin       | 2720216977
active_groups     | [982726092]
public_groups     | 982726092
nfj_group         | 976420087
nfj_bot           | 2720216977
ban_group         | 559046176
nfj_stop          | False
nfj_on            | True
our_server        | {'s1': '10313885910047', 's2': '10312665510199', 's3': '10269676270097', 's4': '10295688560381', 's5': '10306070430418'}
nfj_server        | {'s1': '10307748850667', 's2': '10294976540859', 's3': '10269676270097', 's4': '10295688560381'}
reminder          | True
reminder_groups   | [976420087]
reminder_set      | 
ai_judgement      | https://bfvai.yesu.eu.org/shikanoko/ai_judgment

2025-05-16 03:04:13,514 - __main__ - INFO - 使用绑定地址: 127.0.0.1, 端口: 18889
2025-05-16 03:04:13,514 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-16 03:04:13,514 - __main__ - INFO - 等待 asyncio 事件循环启动...
2025-05-16 03:04:13,515 - __main__ - INFO - Asyncio 事件循环线程启动。
2025-05-16 03:04:13,515 - __main__ - INFO - 主 asyncio 任务启动。
2025-05-16 03:04:13,516 - __main__ - INFO - 提醒循环任务已在主循环中启动。
2025-05-16 03:04:13,516 - __main__ - INFO - 动态命令 's1' 注册成功
2025-05-16 03:04:13,516 - __main__ - INFO - 提醒任务启动。
2025-05-16 03:04:13,516 - __main__ - INFO - 动态命令 's2' 注册成功
2025-05-16 03:04:13,517 - __main__ - INFO - 动态命令 's3' 注册成功
2025-05-16 03:04:13,517 - __main__ - INFO - 动态命令 's4' 注册成功
2025-05-16 03:04:13,517 - __main__ - INFO - 动态命令 's5' 注册成功
2025-05-16 03:04:13,517 - __main__ - INFO - 正在启动 Flask web 服务...
2025-05-16 03:04:16,040 - __main__ - ERROR - game_id 请求API失败: Cannot connect to host api.bfvrobot.net:443 ssl:default [指定的网络名不再可用。]
Traceback (most recent call last):
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 461, in finish_socket_func
    return ov.getresult()
           ^^^^^^^^^^^^^^
OSError: [WinError 64] 指定的网络名不再可用。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1122, in _wrap_create_connection
    return await self._loop.create_connection(*args, **kwargs, sock=sock)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\base_events.py", line 1126, in create_connection
    transport, protocol = await self._create_connection_transport(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\base_events.py", line 1159, in _create_connection_transport
    await waiter
  File "D:\project\python312\Lib\asyncio\proactor_events.py", line 286, in _loop_reading
    length = fut.result()
             ^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 798, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 465, in finish_socket_func
    raise ConnectionResetError(*exc.args)
ConnectionResetError: [WinError 64] 指定的网络名不再可用。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\bfv_ai_bot\niuniubot.py", line 59, in update_game_id
    async with session.get(url) as response:
  File "D:\project\python312\Lib\site-packages\aiohttp\client.py", line 1425, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\client.py", line 703, in _request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 548, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1056, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1400, in _create_direct_connection
    raise last_exc
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1369, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1130, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.bfvrobot.net:443 ssl:default [指定的网络名不再可用。]
2025-05-16 03:04:16,042 - __main__ - ERROR - reminder_set 配置无效，使用默认提醒时间。
2025-05-16 03:04:16,043 - __main__ - INFO - 下一次提醒时间: 2025-05-16 07:01:00 (等待 14203 秒)
2025-05-16 03:06:57,397 - __main__ - INFO - 接收到命令: cnf, 参数: s1, from user: 2720216977, group: 982726092
2025-05-16 03:06:57,397 - __main__ - INFO - 已启动服务器 s1 的暖服状态监控任务
2025-05-16 03:06:57,398 - __main__ - INFO - [s1] 开始监控，每分钟检查，超时30分钟。
2025-05-16 03:06:57,398 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 03:07:36,829 - __main__ - INFO - 接收到命令: cnf, 参数: , from user: 2720216977, group: 982726092
2025-05-16 03:07:57,413 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 03:07:57,416 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 03:07:58,519 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10313885910047 HTTP/1.1" 201 851
2025-05-16 03:07:58,519 - __main__ - ERROR - 尝试 1: 服务器玩家列表请求失败，状态码: 201
2025-05-16 03:07:58,519 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 03:07:59,119 - __main__ - INFO - 接收到命令: scnf, 参数: s1, from user: 2720216977, group: 982726092
2025-05-16 03:07:59,119 - __main__ - INFO - 发送取消信号给服务器 s1 的监控任务
2025-05-16 03:07:59,120 - __main__ - WARNING - [s1] 暖服监控任务被取消。
2025-05-16 03:07:59,120 - __main__ - INFO - [s1] finally 执行了 - 任务结束.
2025-05-16 03:07:59,120 - __main__ - INFO - [s1] 停止监控任务。
2025-05-16 03:08:00,520 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 03:08:01,317 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10313885910047 HTTP/1.1" 201 851
2025-05-16 03:08:01,317 - __main__ - ERROR - 尝试 2: 服务器玩家列表请求失败，状态码: 201
2025-05-16 03:08:01,317 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 03:08:03,318 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 03:08:04,379 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10313885910047 HTTP/1.1" 201 851
2025-05-16 03:08:04,384 - __main__ - ERROR - 尝试 3: 服务器玩家列表请求失败，状态码: 201
2025-05-16 03:08:04,384 - __main__ - ERROR - 服务器玩家列表所有重试失败，无法获取数据。
2025-05-16 03:08:14,850 - __main__ - INFO - 接收到命令: scnf, 参数: s1, from user: 2720216977, group: 982726092
2025-05-16 03:09:22,985 - __main__ - INFO - NiuNiuBot配置加载成功
2025-05-16 03:09:22,985 - __main__ - INFO - 当前的NiuNiuBot配置:
Key               | Value
------------------------
address           | http://127.0.0.1:3000
token             | shikanoko
server_group_name | 2636
qq_bot            | 3989549945
admin_qq          | 350362423
check_admin       | 2720216977
active_groups     | [982726092]
public_groups     | 982726092
nfj_group         | 976420087
nfj_bot           | 2720216977
ban_group         | 559046176
nfj_stop          | False
nfj_on            | True
our_server        | {'s1': '10313885910047', 's2': '10312665510199', 's3': '10269676270097', 's4': '10295688560381', 's5': '10306070430418'}
nfj_server        | {'s1': '10307748850667', 's2': '10294976540859', 's3': '10269676270097', 's4': '10295688560381'}
reminder          | True
reminder_groups   | [976420087]
reminder_set      | 
ai_judgement      | https://bfvai.yesu.eu.org/shikanoko/ai_judgment

2025-05-16 03:09:22,987 - __main__ - INFO - 使用绑定地址: 127.0.0.1, 端口: 18889
2025-05-16 03:09:22,987 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-16 03:09:22,987 - __main__ - INFO - 等待 asyncio 事件循环启动...
2025-05-16 03:09:22,988 - __main__ - INFO - Asyncio 事件循环线程启动。
2025-05-16 03:09:22,988 - __main__ - INFO - 主 asyncio 任务启动。
2025-05-16 03:09:22,988 - __main__ - INFO - 提醒循环任务已在主循环中启动。
2025-05-16 03:09:22,988 - __main__ - INFO - 动态命令 's1' 注册成功
2025-05-16 03:09:22,988 - __main__ - INFO - 提醒任务启动。
2025-05-16 03:09:22,989 - __main__ - INFO - 动态命令 's2' 注册成功
2025-05-16 03:09:22,989 - __main__ - INFO - 动态命令 's3' 注册成功
2025-05-16 03:09:22,990 - __main__ - INFO - 动态命令 's4' 注册成功
2025-05-16 03:09:22,990 - __main__ - INFO - 动态命令 's5' 注册成功
2025-05-16 03:09:22,990 - __main__ - INFO - 正在启动 Flask web 服务...
2025-05-16 03:09:25,559 - __main__ - ERROR - game_id 请求API失败: Cannot connect to host api.bfvrobot.net:443 ssl:default [指定的网络名不再可用。]
Traceback (most recent call last):
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 461, in finish_socket_func
    return ov.getresult()
           ^^^^^^^^^^^^^^
OSError: [WinError 64] 指定的网络名不再可用。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1122, in _wrap_create_connection
    return await self._loop.create_connection(*args, **kwargs, sock=sock)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\base_events.py", line 1126, in create_connection
    transport, protocol = await self._create_connection_transport(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\base_events.py", line 1159, in _create_connection_transport
    await waiter
  File "D:\project\python312\Lib\asyncio\proactor_events.py", line 286, in _loop_reading
    length = fut.result()
             ^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 798, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 465, in finish_socket_func
    raise ConnectionResetError(*exc.args)
ConnectionResetError: [WinError 64] 指定的网络名不再可用。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\bfv_ai_bot\niuniubot.py", line 59, in update_game_id
    async with session.get(url) as response:
  File "D:\project\python312\Lib\site-packages\aiohttp\client.py", line 1425, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\client.py", line 703, in _request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 548, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1056, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1400, in _create_direct_connection
    raise last_exc
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1369, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1130, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.bfvrobot.net:443 ssl:default [指定的网络名不再可用。]
2025-05-16 03:09:25,563 - __main__ - ERROR - reminder_set 配置无效，使用默认提醒时间。
2025-05-16 03:09:25,563 - __main__ - INFO - 下一次提醒时间: 2025-05-16 07:01:00 (等待 13894 秒)
2025-05-16 03:09:34,042 - __main__ - INFO - 接收到命令: cnf, 参数: s1, from user: 2720216977, group: 982726092
2025-05-16 03:09:34,043 - __main__ - INFO - 已启动服务器 s1 的暖服状态监控任务
2025-05-16 03:09:34,043 - __main__ - INFO - [s1] 开始监控，每分钟检查，超时30分钟。
2025-05-16 03:09:34,043 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 03:09:38,083 - __main__ - INFO - 接收到命令: scnf, 参数: s1, from user: 2720216977, group: 982726092
2025-05-16 03:09:38,083 - __main__ - INFO - 发送取消信号给服务器 s1 的监控任务
2025-05-16 03:09:38,083 - __main__ - WARNING - [s1] 暖服监控任务被取消。
2025-05-16 03:09:38,083 - __main__ - INFO - [s1] finally 执行了 - 任务结束.
2025-05-16 03:09:38,084 - __main__ - INFO - [s1] 停止监控任务。
2025-05-16 03:10:35,818 - __main__ - INFO - 接收到命令: cnf, 参数: s1, from user: 2720216977, group: 982726092
2025-05-16 03:10:35,819 - __main__ - INFO - 已启动服务器 s1 的暖服状态监控任务
2025-05-16 03:10:35,819 - __main__ - INFO - [s1] 开始监控，每分钟检查，超时30分钟。
2025-05-16 03:10:35,820 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 03:10:38,692 - __main__ - INFO - 接收到命令: cnf, 参数: s2, from user: 2720216977, group: 982726092
2025-05-16 03:10:38,692 - __main__ - INFO - 已启动服务器 s2 的暖服状态监控任务
2025-05-16 03:10:38,693 - __main__ - INFO - [s2] 开始监控，每分钟检查，超时30分钟。
2025-05-16 03:10:38,693 - __main__ - DEBUG - [s2] 等待 60 秒...
2025-05-16 03:10:42,240 - __main__ - INFO - 接收到命令: cnf, 参数: s3, from user: 2720216977, group: 982726092
2025-05-16 03:10:42,240 - __main__ - INFO - 已启动服务器 s3 的暖服状态监控任务
2025-05-16 03:10:42,241 - __main__ - INFO - [s3] 开始监控，每分钟检查，超时30分钟。
2025-05-16 03:10:42,241 - __main__ - DEBUG - [s3] 等待 60 秒...
2025-05-16 03:10:44,781 - __main__ - INFO - 接收到命令: cnf, 参数: , from user: 2720216977, group: 982726092
2025-05-16 03:11:35,846 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 03:11:35,849 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 03:11:37,023 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10313885910047 HTTP/1.1" 201 851
2025-05-16 03:11:37,023 - __main__ - ERROR - 尝试 1: 服务器玩家列表请求失败，状态码: 201
2025-05-16 03:11:37,024 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 03:11:38,711 - __main__ - DEBUG - [s2] 等待结束，检查状态...
2025-05-16 03:11:38,713 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 03:11:39,028 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 03:11:39,695 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10312665510199 HTTP/1.1" 201 869
2025-05-16 03:11:39,710 - __main__ - ERROR - 尝试 1: 服务器玩家列表请求失败，状态码: 201
2025-05-16 03:11:39,710 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 03:11:39,808 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10313885910047 HTTP/1.1" 201 851
2025-05-16 03:11:39,809 - __main__ - ERROR - 尝试 2: 服务器玩家列表请求失败，状态码: 201
2025-05-16 03:11:39,810 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 03:11:41,716 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 03:11:41,811 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 03:11:42,248 - __main__ - DEBUG - [s3] 等待结束，检查状态...
2025-05-16 03:11:42,250 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 03:11:42,466 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10312665510199 HTTP/1.1" 201 869
2025-05-16 03:11:42,468 - __main__ - ERROR - 尝试 2: 服务器玩家列表请求失败，状态码: 201
2025-05-16 03:11:42,468 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 03:11:42,843 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10313885910047 HTTP/1.1" 201 851
2025-05-16 03:11:42,847 - __main__ - ERROR - 尝试 3: 服务器玩家列表请求失败，状态码: 201
2025-05-16 03:11:42,848 - __main__ - ERROR - 服务器玩家列表所有重试失败，无法获取数据。
2025-05-16 03:11:42,848 - __main__ - WARNING - check_nuanfu_status for s1: async_get_players_gametools 返回无效数据。
2025-05-16 03:11:42,848 - __main__ - ERROR - [s1] 检查暖服状态失败: 无法获取有效的玩家数据。 - 监控将继续每分钟重试
2025-05-16 03:11:42,848 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 03:11:43,696 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10269676270097 HTTP/1.1" 201 890
2025-05-16 03:11:43,696 - __main__ - ERROR - 尝试 1: 服务器玩家列表请求失败，状态码: 201
2025-05-16 03:11:43,696 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 03:11:44,469 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 03:11:45,696 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 03:11:45,728 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10312665510199 HTTP/1.1" 201 869
2025-05-16 03:11:45,729 - __main__ - ERROR - 尝试 3: 服务器玩家列表请求失败，状态码: 201
2025-05-16 03:11:45,729 - __main__ - ERROR - 服务器玩家列表所有重试失败，无法获取数据。
2025-05-16 03:11:45,729 - __main__ - WARNING - check_nuanfu_status for s2: async_get_players_gametools 返回无效数据。
2025-05-16 03:11:45,729 - __main__ - ERROR - [s2] 检查暖服状态失败: 无法获取有效的玩家数据。 - 监控将继续每分钟重试
2025-05-16 03:11:45,729 - __main__ - DEBUG - [s2] 等待 60 秒...
2025-05-16 03:11:46,499 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10269676270097 HTTP/1.1" 201 890
2025-05-16 03:11:46,500 - __main__ - ERROR - 尝试 2: 服务器玩家列表请求失败，状态码: 201
2025-05-16 03:11:46,500 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 03:11:48,501 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 03:11:50,154 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10269676270097 HTTP/1.1" 201 890
2025-05-16 03:11:50,155 - __main__ - ERROR - 尝试 3: 服务器玩家列表请求失败，状态码: 201
2025-05-16 03:11:50,155 - __main__ - ERROR - 服务器玩家列表所有重试失败，无法获取数据。
2025-05-16 03:11:50,155 - __main__ - WARNING - check_nuanfu_status for s3: async_get_players_gametools 返回无效数据。
2025-05-16 03:11:50,155 - __main__ - ERROR - [s3] 检查暖服状态失败: 无法获取有效的玩家数据。 - 监控将继续每分钟重试
2025-05-16 03:11:50,156 - __main__ - DEBUG - [s3] 等待 60 秒...
2025-05-16 03:12:01,010 - __main__ - INFO - 接收到命令: nfj, 参数: s5, from user: 2720216977, group: 982726092
2025-05-16 13:59:29,613 - __main__ - INFO - NiuNiuBot配置加载成功
2025-05-16 13:59:29,613 - __main__ - INFO - 当前的NiuNiuBot配置:
Key               | Value
------------------------
address           | http://127.0.0.1:3000
token             | shikanoko
server_group_name | 2636
qq_bot            | 3989549945
admin_qq          | 350362423
check_admin       | 2720216977
active_groups     | [982726092]
public_groups     | 982726092
nfj_group         | 976420087
nfj_bot           | 2720216977
ban_group         | 559046176
nfj_stop          | False
nfj_on            | True
our_server        | {'s1': '10313885910047', 's2': '10312665510199', 's3': '10269676270097', 's4': '10295688560381', 's5': '10306070430418'}
nfj_server        | {'s1': '10307748850667', 's2': '10294976540859', 's3': '10269676270097', 's4': '10295688560381'}
reminder          | True
reminder_groups   | [976420087]
reminder_set      | 
ai_judgement      | https://bfvai.yesu.eu.org/shikanoko/ai_judgment

2025-05-16 13:59:29,614 - __main__ - INFO - 使用绑定地址: 127.0.0.1, 端口: 18889
2025-05-16 13:59:29,614 - __main__ - INFO - 等待 asyncio 事件循环启动...
2025-05-16 13:59:29,614 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-16 13:59:29,615 - __main__ - INFO - Asyncio 事件循环线程启动。
2025-05-16 13:59:29,615 - __main__ - INFO - 主 asyncio 任务启动。
2025-05-16 13:59:29,615 - __main__ - INFO - 提醒循环任务已在主循环中启动。
2025-05-16 13:59:29,616 - __main__ - INFO - 动态命令 's1' 注册成功
2025-05-16 13:59:29,616 - __main__ - INFO - 提醒任务启动。
2025-05-16 13:59:29,616 - __main__ - INFO - 动态命令 's2' 注册成功
2025-05-16 13:59:29,616 - __main__ - INFO - 动态命令 's3' 注册成功
2025-05-16 13:59:29,616 - __main__ - INFO - 动态命令 's4' 注册成功
2025-05-16 13:59:29,616 - __main__ - INFO - 动态命令 's5' 注册成功
2025-05-16 13:59:29,616 - __main__ - INFO - 正在启动 Flask web 服务...
2025-05-16 13:59:32,975 - __main__ - INFO - config.json 加载成功
2025-05-16 13:59:32,976 - __main__ - INFO - 更新 s1 的 gameId: 10313885910047 -> 10314970980229
2025-05-16 13:59:32,976 - __main__ - INFO - s2 的 gameId 未变化: 10312665510199
2025-05-16 13:59:32,976 - __main__ - INFO - s3 的 gameId 未变化: 10269676270097
2025-05-16 13:59:32,976 - __main__ - INFO - s4 的 gameId 未变化: 10295688560381
2025-05-16 13:59:32,976 - __main__ - INFO - 更新 s5 的 gameId: 10306070430418 -> 10315527430994
2025-05-16 13:59:32,976 - __main__ - INFO - config.json 更新成功
2025-05-16 13:59:32,976 - __main__ - INFO - NiuNiuBot 配置重新加载成功
2025-05-16 13:59:32,976 - __main__ - ERROR - reminder_set 配置无效，使用默认提醒时间。
2025-05-16 13:59:32,976 - __main__ - INFO - 下一次提醒时间: 2025-05-16 14:01:00 (等待 87 秒)
2025-05-16 13:59:48,808 - __main__ - INFO - 接收到命令: cnf, 参数: s1, from user: 2720216977, group: 982726092
2025-05-16 13:59:48,808 - __main__ - INFO - 已启动服务器 s1 的暖服状态监控任务
2025-05-16 13:59:48,809 - __main__ - INFO - [s1] 开始监控，每分钟检查，超时30分钟。
2025-05-16 13:59:48,809 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:00:48,822 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:00:48,826 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:00:50,214 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:00:50,221 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:00:50,221 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:00:54,658 - __main__ - INFO - 接收到命令: snf, 参数: s1, from user: 2720216977, group: 982726092
2025-05-16 14:00:54,659 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:00:55,731 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:01:00,007 - root - ERROR - help.txt 文件未找到，请检查文件是否存在。
2025-05-16 14:01:01,825 - __main__ - INFO - config.json 加载成功
2025-05-16 14:01:01,825 - __main__ - INFO - s1 的 gameId 未变化: 10314970980229
2025-05-16 14:01:01,825 - __main__ - INFO - s2 的 gameId 未变化: 10312665510199
2025-05-16 14:01:01,826 - __main__ - INFO - s3 的 gameId 未变化: 10269676270097
2025-05-16 14:01:01,826 - __main__ - INFO - s4 的 gameId 未变化: 10295688560381
2025-05-16 14:01:01,827 - __main__ - INFO - s5 的 gameId 未变化: 10315527430994
2025-05-16 14:01:01,827 - __main__ - INFO - NiuNiuBot 配置重新加载成功
2025-05-16 14:01:02,336 - __main__ - INFO - 已发送提醒到群组 976420087
2025-05-16 14:01:02,337 - __main__ - ERROR - reminder_set 配置无效，使用默认提醒时间。
2025-05-16 14:01:02,338 - __main__ - INFO - 下一次提醒时间: 2025-05-16 15:01:00 (等待 3597 秒)
2025-05-16 14:01:50,214 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:01:50,217 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:01:57,398 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 201 851
2025-05-16 14:01:57,399 - __main__ - ERROR - 尝试 1: 服务器玩家列表请求失败，状态码: 201
2025-05-16 14:01:57,399 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 14:01:59,400 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:02:00,654 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 201 851
2025-05-16 14:02:00,654 - __main__ - ERROR - 尝试 2: 服务器玩家列表请求失败，状态码: 201
2025-05-16 14:02:00,654 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 14:02:02,655 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:02:03,544 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 201 851
2025-05-16 14:02:03,546 - __main__ - ERROR - 尝试 3: 服务器玩家列表请求失败，状态码: 201
2025-05-16 14:02:03,547 - __main__ - ERROR - 服务器玩家列表所有重试失败，无法获取数据。
2025-05-16 14:02:03,548 - __main__ - WARNING - check_nuanfu_status for s1: async_get_players_gametools 返回无效数据。
2025-05-16 14:02:03,549 - __main__ - ERROR - [s1] 检查暖服状态失败: 无法获取有效的玩家数据。 - 监控将继续每分钟重试
2025-05-16 14:02:03,549 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:03:03,560 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:03:03,562 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:03:05,171 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:03:05,174 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:03:05,175 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:04:05,182 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:04:05,183 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:04:08,195 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:04:08,197 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:04:08,197 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:05:08,208 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:05:08,210 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:05:09,656 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:05:09,660 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:05:09,660 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:06:09,668 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:06:09,669 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:06:10,726 - __main__ - INFO - 接收到命令: snf, 参数: s1, from user: 2720216977, group: 982726092
2025-05-16 14:06:10,728 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:06:11,096 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:06:11,098 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:06:11,098 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:06:11,629 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:07:11,100 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:07:11,103 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:07:14,642 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:07:14,644 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:07:14,644 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:08:14,651 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:08:14,651 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:08:16,036 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:08:16,038 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:08:16,038 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:09:16,038 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:09:16,040 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:09:17,470 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:09:17,868 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:09:17,868 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:10:17,885 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:10:17,887 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:10:20,550 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:10:20,552 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:10:20,553 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:11:20,576 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:11:20,577 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:11:21,615 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:11:21,617 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:11:21,617 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:12:21,630 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:12:21,632 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:12:28,165 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:12:28,166 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:12:28,166 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:13:28,174 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:13:28,176 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:13:35,795 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:13:35,797 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:13:35,797 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:14:35,802 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:14:35,804 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:14:42,415 - __main__ - ERROR - 尝试 1: 服务器玩家列表请求异常: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 488, in _make_request
    raise new_e
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 741, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 920, in _ssl_wrap_socket_and_match_hostname
    ssl_sock = ssl_wrap_socket(
               ^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\util\ssl_.py", line 460, in ssl_wrap_socket
    ssl_sock = _ssl_wrap_socket_impl(sock, context, tls_in_tls, server_hostname)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\util\ssl_.py", line 504, in _ssl_wrap_socket_impl
    return ssl_context.wrap_socket(sock, server_hostname=server_hostname)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\ssl.py", line 455, in wrap_socket
    return self.sslsocket_class._create(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\ssl.py", line 1046, in _create
    self.do_handshake()
  File "D:\project\python312\Lib\ssl.py", line 1317, in do_handshake
    self._sslobj.do_handshake()
ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\util\util.py", line 38, in reraise
    raise value.with_traceback(tb)
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 488, in _make_request
    raise new_e
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 741, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 920, in _ssl_wrap_socket_and_match_hostname
    ssl_sock = ssl_wrap_socket(
               ^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\util\ssl_.py", line 460, in ssl_wrap_socket
    ssl_sock = _ssl_wrap_socket_impl(sock, context, tls_in_tls, server_hostname)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\util\ssl_.py", line 504, in _ssl_wrap_socket_impl
    return ssl_context.wrap_socket(sock, server_hostname=server_hostname)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\ssl.py", line 455, in wrap_socket
    return self.sslsocket_class._create(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\ssl.py", line 1046, in _create
    self.do_handshake()
  File "D:\project\python312\Lib\ssl.py", line 1317, in do_handshake
    self._sslobj.do_handshake()
urllib3.exceptions.ProtocolError: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\bfv_ai_bot\new_niuniubot.py", line 434, in get_players_gametools
    response = requests.get(url, timeout=timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\adapters.py", line 682, in send
    raise ConnectionError(err, request=request)
requests.exceptions.ConnectionError: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-05-16 14:14:42,434 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 14:14:44,437 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:14:45,897 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:14:45,899 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:14:45,900 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:15:21,405 - __main__ - INFO - 接收到命令: snf, 参数: s1, from user: 2720216977, group: 982726092
2025-05-16 14:15:21,406 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:15:22,680 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:15:45,912 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:15:45,915 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:15:46,798 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:15:46,799 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:15:46,799 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:16:46,810 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:16:46,814 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:17:03,500 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:17:03,501 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:17:03,502 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:18:03,503 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:18:03,505 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:18:04,842 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:18:04,843 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:18:04,844 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:19:04,851 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:19:04,851 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:19:12,099 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:19:12,101 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:19:12,101 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:20:12,104 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:20:12,106 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:20:13,515 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:20:13,517 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:20:13,517 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:21:13,520 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:21:13,522 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:21:24,604 - __main__ - ERROR - 尝试 1: 服务器玩家列表请求异常: HTTPSConnectionPool(host='api.gametools.network', port=443): Max retries exceeded with url: /bfv/players/?gameid=10314970980229 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000186CBEE03B0>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)"))
Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\util\connection.py", line 60, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\socket.py", line 963, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 488, in _make_request
    raise new_e
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 205, in _new_conn
    raise NameResolutionError(self.host, self, e) from e
urllib3.exceptions.NameResolutionError: <urllib3.connection.HTTPSConnection object at 0x00000186CBEE03B0>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.gametools.network', port=443): Max retries exceeded with url: /bfv/players/?gameid=10314970980229 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000186CBEE03B0>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)"))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\bfv_ai_bot\new_niuniubot.py", line 434, in get_players_gametools
    response = requests.get(url, timeout=timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='api.gametools.network', port=443): Max retries exceeded with url: /bfv/players/?gameid=10314970980229 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000186CBEE03B0>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)"))
2025-05-16 14:21:24,609 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 14:21:26,610 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:21:26,610 - __main__ - ERROR - 尝试 2: 服务器玩家列表请求异常: HTTPSConnectionPool(host='api.gametools.network', port=443): Max retries exceeded with url: /bfv/players/?gameid=10314970980229 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000186CBEF7140>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)"))
Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\util\connection.py", line 60, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\socket.py", line 963, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 488, in _make_request
    raise new_e
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 205, in _new_conn
    raise NameResolutionError(self.host, self, e) from e
urllib3.exceptions.NameResolutionError: <urllib3.connection.HTTPSConnection object at 0x00000186CBEF7140>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.gametools.network', port=443): Max retries exceeded with url: /bfv/players/?gameid=10314970980229 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000186CBEF7140>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)"))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\bfv_ai_bot\new_niuniubot.py", line 434, in get_players_gametools
    response = requests.get(url, timeout=timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='api.gametools.network', port=443): Max retries exceeded with url: /bfv/players/?gameid=10314970980229 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000186CBEF7140>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)"))
2025-05-16 14:21:26,616 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 14:21:28,618 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:21:28,618 - __main__ - ERROR - 尝试 3: 服务器玩家列表请求异常: HTTPSConnectionPool(host='api.gametools.network', port=443): Max retries exceeded with url: /bfv/players/?gameid=10314970980229 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000186CBEF70B0>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)"))
Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\util\connection.py", line 60, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\socket.py", line 963, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 488, in _make_request
    raise new_e
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 205, in _new_conn
    raise NameResolutionError(self.host, self, e) from e
urllib3.exceptions.NameResolutionError: <urllib3.connection.HTTPSConnection object at 0x00000186CBEF70B0>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.gametools.network', port=443): Max retries exceeded with url: /bfv/players/?gameid=10314970980229 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000186CBEF70B0>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)"))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\bfv_ai_bot\new_niuniubot.py", line 434, in get_players_gametools
    response = requests.get(url, timeout=timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='api.gametools.network', port=443): Max retries exceeded with url: /bfv/players/?gameid=10314970980229 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000186CBEF70B0>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)"))
2025-05-16 14:21:28,622 - __main__ - ERROR - 服务器玩家列表所有重试失败，无法获取数据。
2025-05-16 14:21:28,622 - __main__ - WARNING - check_nuanfu_status for s1: async_get_players_gametools 返回无效数据。
2025-05-16 14:21:28,622 - __main__ - ERROR - [s1] 检查暖服状态失败: 无法获取有效的玩家数据。 - 监控将继续每分钟重试
2025-05-16 14:21:28,622 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:22:28,624 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:22:28,625 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:22:28,626 - __main__ - ERROR - 尝试 1: 服务器玩家列表请求异常: HTTPSConnectionPool(host='api.gametools.network', port=443): Max retries exceeded with url: /bfv/players/?gameid=10314970980229 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000186CBEF6B70>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)"))
Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\util\connection.py", line 60, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\socket.py", line 963, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 488, in _make_request
    raise new_e
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 205, in _new_conn
    raise NameResolutionError(self.host, self, e) from e
urllib3.exceptions.NameResolutionError: <urllib3.connection.HTTPSConnection object at 0x00000186CBEF6B70>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.gametools.network', port=443): Max retries exceeded with url: /bfv/players/?gameid=10314970980229 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000186CBEF6B70>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)"))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\bfv_ai_bot\new_niuniubot.py", line 434, in get_players_gametools
    response = requests.get(url, timeout=timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='api.gametools.network', port=443): Max retries exceeded with url: /bfv/players/?gameid=10314970980229 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000186CBEF6B70>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)"))
2025-05-16 14:22:28,632 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 14:22:30,633 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:22:30,634 - __main__ - ERROR - 尝试 2: 服务器玩家列表请求异常: HTTPSConnectionPool(host='api.gametools.network', port=443): Max retries exceeded with url: /bfv/players/?gameid=10314970980229 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000186CBEF5D00>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)"))
Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\util\connection.py", line 60, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\socket.py", line 963, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 488, in _make_request
    raise new_e
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 205, in _new_conn
    raise NameResolutionError(self.host, self, e) from e
urllib3.exceptions.NameResolutionError: <urllib3.connection.HTTPSConnection object at 0x00000186CBEF5D00>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.gametools.network', port=443): Max retries exceeded with url: /bfv/players/?gameid=10314970980229 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000186CBEF5D00>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)"))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\bfv_ai_bot\new_niuniubot.py", line 434, in get_players_gametools
    response = requests.get(url, timeout=timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='api.gametools.network', port=443): Max retries exceeded with url: /bfv/players/?gameid=10314970980229 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000186CBEF5D00>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)"))
2025-05-16 14:22:30,642 - __main__ - INFO - 等待 2 秒后重试...
2025-05-16 14:22:32,643 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:22:32,644 - __main__ - ERROR - 尝试 3: 服务器玩家列表请求异常: HTTPSConnectionPool(host='api.gametools.network', port=443): Max retries exceeded with url: /bfv/players/?gameid=10314970980229 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000186CBEE29F0>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)"))
Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\util\connection.py", line 60, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\socket.py", line 963, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 488, in _make_request
    raise new_e
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 704, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connection.py", line 205, in _new_conn
    raise NameResolutionError(self.host, self, e) from e
urllib3.exceptions.NameResolutionError: <urllib3.connection.HTTPSConnection object at 0x00000186CBEE29F0>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.gametools.network', port=443): Max retries exceeded with url: /bfv/players/?gameid=10314970980229 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000186CBEE29F0>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)"))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\bfv_ai_bot\new_niuniubot.py", line 434, in get_players_gametools
    response = requests.get(url, timeout=timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\requests\adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='api.gametools.network', port=443): Max retries exceeded with url: /bfv/players/?gameid=10314970980229 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000186CBEE29F0>: Failed to resolve 'api.gametools.network' ([Errno 11001] getaddrinfo failed)"))
2025-05-16 14:22:32,647 - __main__ - ERROR - 服务器玩家列表所有重试失败，无法获取数据。
2025-05-16 14:22:32,647 - __main__ - WARNING - check_nuanfu_status for s1: async_get_players_gametools 返回无效数据。
2025-05-16 14:22:32,647 - __main__ - ERROR - [s1] 检查暖服状态失败: 无法获取有效的玩家数据。 - 监控将继续每分钟重试
2025-05-16 14:22:32,648 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:23:32,652 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:23:32,653 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:23:39,407 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:23:39,410 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:23:39,411 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:24:39,414 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:24:39,417 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:24:44,920 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:24:44,921 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:24:44,921 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:25:44,932 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:25:44,933 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:25:46,833 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:25:46,834 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:25:46,835 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:26:46,841 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:26:46,844 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:26:48,235 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:26:48,237 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:26:48,238 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:27:48,252 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:27:48,254 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:27:50,662 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:27:50,663 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:27:50,663 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:28:50,678 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:28:50,679 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.gametools.network:443
2025-05-16 14:28:52,417 - urllib3.connectionpool - DEBUG - https://api.gametools.network:443 "GET /bfv/players/?gameid=10314970980229 HTTP/1.1" 200 None
2025-05-16 14:28:52,419 - __main__ - INFO - [s1] 当前暖服状态: 正在暖服
2025-05-16 14:28:52,420 - __main__ - DEBUG - [s1] 等待 60 秒...
2025-05-16 14:29:52,431 - __main__ - DEBUG - [s1] 等待结束，检查状态...
2025-05-16 14:29:52,431 - __main__ - INFO - [s1] 监控超时。
2025-05-16 14:29:52,908 - __main__ - INFO - [s1] finally 执行了 - 任务结束.
2025-05-16 14:29:52,909 - __main__ - INFO - [s1] 停止监控任务。
2025-05-16 15:01:00,020 - root - ERROR - help.txt 文件未找到，请检查文件是否存在。
2025-05-16 15:01:00,021 - __main__ - ERROR - game_id 请求API失败: Cannot connect to host api.bfvrobot.net:443 ssl:default [getaddrinfo failed]
Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1351, in _create_direct_connection
    hosts = await self._resolve_host(host, port, traces=traces)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 995, in _resolve_host
    return await asyncio.shield(resolved_host_task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1026, in _resolve_host_with_throttle
    addrs = await self._resolver.resolve(host, port, family=self._family)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\resolver.py", line 36, in resolve
    infos = await self._loop.getaddrinfo(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\base_events.py", line 878, in getaddrinfo
    return await self.run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\socket.py", line 963, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\bfv_ai_bot\new_niuniubot.py", line 59, in update_game_id
    async with session.get(url) as response:
  File "D:\project\python312\Lib\site-packages\aiohttp\client.py", line 1425, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\client.py", line 703, in _request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 548, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1056, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1357, in _create_direct_connection
    raise ClientConnectorDNSError(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorDNSError: Cannot connect to host api.bfvrobot.net:443 ssl:default [getaddrinfo failed]
2025-05-16 15:01:10,036 - __main__ - ERROR - send_group_msg: 发送到群组 976420087 失败: Timeout: NTEvent serviceAndMethod:NodeIKernelMsgService/sendMsg ListenerName:NodeIKernelMsgListener/onMsgInfoListUpdate EventRet:
{}
, 错误码: 200
2025-05-16 15:01:10,037 - __main__ - INFO - 已发送提醒到群组 976420087
2025-05-16 15:01:10,038 - __main__ - ERROR - reminder_set 配置无效，使用默认提醒时间。
2025-05-16 15:01:10,039 - __main__ - INFO - 下一次提醒时间: 2025-05-16 16:01:00 (等待 3589 秒)
2025-05-26 01:28:20,359 - __main__ - INFO - NiuNiuBot配置加载成功
2025-05-26 01:28:20,359 - __main__ - INFO - 当前的NiuNiuBot配置:
Key               | Value
------------------------
address           | http://127.0.0.1:3000
token             | 
server_group_name | 2636
qq_bot            | 3626552990
admin_qq          | 350362423
check_admin       | 2720216977
active_groups     | [978880814, 976420087, 1037749375, 1050955830]
public_groups     | 978880814
nfj_group         | 976420087
nfj_bot           | 2720216977
ban_group         | 559046176
nfj_stop          | False
nfj_on            | True
our_server        | {'s1': '10328172600595', 's2': '10335373600350', 's3': '10269676270097', 's4': '10335901330180', 's5': '10327786490348'}
nfj_server        | {'s1': '10307748850667', 's2': '10294976540859', 's3': '10269676270097', 's4': '10295688560381'}
reminder          | True
reminder_groups   | [976420087]
reminder_set      | 
ai_judgement      | https://bfvai.yesu.eu.org/shikanoko/ai_judgment

2025-05-26 01:28:20,360 - __main__ - INFO - 使用绑定地址: 127.0.0.1, 端口: 18889
2025-05-26 01:28:20,360 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-26 01:28:20,360 - __main__ - INFO - 等待 asyncio 事件循环启动...
2025-05-26 01:28:20,361 - __main__ - INFO - Asyncio 事件循环线程启动。
2025-05-26 01:28:20,362 - __main__ - INFO - 主 asyncio 任务启动。
2025-05-26 01:28:20,362 - __main__ - INFO - 提醒循环任务已在主循环中启动。
2025-05-26 01:28:20,362 - __main__ - INFO - 动态命令 's1' 注册成功
2025-05-26 01:28:20,362 - __main__ - INFO - 提醒任务启动。
2025-05-26 01:28:20,362 - __main__ - INFO - 动态命令 's2' 注册成功
2025-05-26 01:28:20,363 - __main__ - INFO - 动态命令 's3' 注册成功
2025-05-26 01:28:20,363 - __main__ - INFO - 动态命令 's4' 注册成功
2025-05-26 01:28:20,363 - __main__ - INFO - 动态命令 's5' 注册成功
2025-05-26 01:28:20,364 - __main__ - INFO - 正在启动 Flask web 服务...
2025-05-26 01:28:22,140 - __main__ - INFO - config.json 加载成功
2025-05-26 01:28:22,140 - __main__ - INFO - s1 的 gameId 未变化: 10328172600595
2025-05-26 01:28:22,140 - __main__ - INFO - s2 的 gameId 未变化: 10335373600350
2025-05-26 01:28:22,140 - __main__ - INFO - s3 的 gameId 未变化: 10269676270097
2025-05-26 01:28:22,140 - __main__ - INFO - s4 的 gameId 未变化: 10335901330180
2025-05-26 01:28:22,141 - __main__ - INFO - s5 的 gameId 未变化: 10327786490348
2025-05-26 01:28:22,141 - __main__ - INFO - NiuNiuBot 配置重新加载成功
2025-05-26 01:28:22,141 - __main__ - ERROR - reminder_set 配置无效，使用默认提醒时间。
2025-05-26 01:28:22,141 - __main__ - INFO - 下一次提醒时间: 2025-05-26 07:01:00 (等待 19957 秒)
2025-05-26 01:29:01,315 - __main__ - INFO - 接收到命令: 2, 参数: , from user: 2720216977, group: 976420087
2025-05-26 01:29:27,983 - __main__ - INFO - 接收到命令: bc, 参数: ccyniuniu, from user: 2720216977, group: 976420087
2025-05-26 01:29:52,702 - __main__ - INFO - NiuNiuBot配置加载成功
2025-05-26 01:29:52,703 - __main__ - INFO - 当前的NiuNiuBot配置:
Key               | Value
------------------------
address           | http://127.0.0.1:3000
token             | 
server_group_name | 2636
qq_bot            | 3626552990
admin_qq          | 350362423
check_admin       | 2720216977
active_groups     | [978880814, 976420087, 1037749375, 1050955830]
public_groups     | 978880814
nfj_group         | 976420087
nfj_bot           | 2720216977
ban_group         | 559046176
nfj_stop          | False
nfj_on            | True
our_server        | {'s1': '10328172600595', 's2': '10335373600350', 's3': '10269676270097', 's4': '10335901330180', 's5': '10327786490348'}
nfj_server        | {'s1': '10307748850667', 's2': '10294976540859', 's3': '10269676270097', 's4': '10295688560381'}
reminder          | True
reminder_groups   | [976420087]
reminder_set      | 
ai_judgement      | https://bfvai.yesu.eu.org/shikanoko/ai_judgment

2025-05-26 01:29:52,703 - __main__ - INFO - 使用绑定地址: 127.0.0.1, 端口: 18889
2025-05-26 01:29:52,704 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-26 01:29:52,704 - __main__ - INFO - 等待 asyncio 事件循环启动...
2025-05-26 01:29:52,705 - __main__ - INFO - Asyncio 事件循环线程启动。
2025-05-26 01:29:52,705 - __main__ - INFO - 主 asyncio 任务启动。
2025-05-26 01:29:52,705 - __main__ - INFO - 提醒循环任务已在主循环中启动。
2025-05-26 01:29:52,705 - __main__ - INFO - 动态命令 's1' 注册成功
2025-05-26 01:29:52,705 - __main__ - INFO - 提醒任务启动。
2025-05-26 01:29:52,705 - __main__ - INFO - 动态命令 's2' 注册成功
2025-05-26 01:29:52,706 - __main__ - INFO - 动态命令 's3' 注册成功
2025-05-26 01:29:52,706 - __main__ - INFO - 动态命令 's4' 注册成功
2025-05-26 01:29:52,707 - __main__ - INFO - 动态命令 's5' 注册成功
2025-05-26 01:29:52,707 - __main__ - INFO - 正在启动 Flask web 服务...
2025-05-26 01:29:57,197 - __main__ - INFO - 接收到命令: bc, 参数: ccyniuniu, from user: 2720216977, group: 976420087
2025-05-26 01:30:16,126 - __main__ - ERROR - game_id 请求API失败: Cannot connect to host api.bfvrobot.net:443 ssl:default [信号灯超时时间已到]
Traceback (most recent call last):
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1115, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohappyeyeballs\impl.py", line 104, in start_connection
    raise first_exception
  File "D:\project\python312\Lib\site-packages\aiohappyeyeballs\impl.py", line 82, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohappyeyeballs\impl.py", line 174, in _connect_sock
    await loop.sock_connect(sock, address)
  File "D:\project\python312\Lib\asyncio\proactor_events.py", line 727, in sock_connect
    return await self._proactor.connect(sock, address)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 798, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\asyncio\windows_events.py", line 594, in finish_connect
    ov.getresult()
OSError: [WinError 121] 信号灯超时时间已到

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\bfv_ai_bot\new_niuniubot.py", line 59, in update_game_id
    async with session.get(url) as response:
  File "D:\project\python312\Lib\site-packages\aiohttp\client.py", line 1425, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\client.py", line 703, in _request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 548, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1056, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1400, in _create_direct_connection
    raise last_exc
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1369, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\python312\Lib\site-packages\aiohttp\connector.py", line 1130, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.bfvrobot.net:443 ssl:default [信号灯超时时间已到]
2025-05-26 01:30:16,131 - __main__ - ERROR - reminder_set 配置无效，使用默认提醒时间。
2025-05-26 01:30:16,131 - __main__ - INFO - 下一次提醒时间: 2025-05-26 07:01:00 (等待 19843 秒)
2025-05-26 01:30:29,727 - __main__ - INFO - 接收到命令: bc, 参数: XiaooooMoooo, from user: 2720216977, group: 978880814
2025-05-26 01:30:50,160 - __main__ - INFO - 接收到命令: bc, 参数: hying15, from user: 2720216977, group: 978880814
2025-05-26 01:31:37,939 - __main__ - INFO - 接收到命令: bc, 参数: halohan8, from user: 2720216977, group: 978880814
2025-05-26 01:33:25,381 - __main__ - INFO - Flask web 服务已停止。
