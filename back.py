import shutil
import subprocess
import threading
import time
import os
import json
import base64
import re
import logging
import asyncio
import aiohttp  # 异步 HTTP 库
import requests
from PIL import Image  # 用于图片处理，需安装 Pillow 库
from flask import Flask, request, jsonify
import datetime
from math import ceil
import argparse  # 添加 argparse 模块，用于解析命令行参数
# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
app = Flask(__name__)
# 加载配置从config.json文件
try:
    with open('config.json', 'r') as f:
        NIUNIUBOT_CONFIG = json.load(f)
    logger.info("NiuNiuBot配置加载成功")
except FileNotFoundError:
    logger.error("错误：config.json 文件未找到！请确保文件存在于当前目录。")
    NIUNIUBOT_CONFIG = {}  # 或设置默认配置
except json.JSONDecodeError:
    logger.error("错误：config.json 文件的JSON格式无效！请检查文件内容。")
    NIUNIUBOT_CONFIG = {}  # 或设置默认配置

# 定义一个函数来打印配置为表格格式
def print_config_table(config):
    if not config:
        return "无配置可用。"
    max_key_length = max(len(str(key)) for key in config.keys())
    table_str = f"{'Key'.ljust(max_key_length)} | Value\n"
    table_str += "-" * (max_key_length + 7) + "\n"
    for key, value in config.items():
        value_str = str(value)
        table_str += f"{str(key).ljust(max_key_length)} | {value_str}\n"
    return table_str

# 打印配置信息为表格格式
logger.info("当前的NiuNiuBot配置:\n" + print_config_table(NIUNIUBOT_CONFIG))
# 命令处理函数映射
command_handlers = {}

# 注册命令处理函数
def register_command(command_name):
    def decorator(func):
        command_handlers[command_name] = func
        return func
    return decorator

# 创建全局锁，用于保护文件操作
file_lock = threading.Lock()

# 新增函数：获取帮助文本
def get_help_text(config):
    our_server_config = config.get("our_server", {})
    if not isinstance(our_server_config, dict):
        available_servers = None
    else:
        available_servers = ', '.join(our_server_config.keys())
    help_text = f"欢迎来到我们群组\n当前已有的服务器:{available_servers}\n可用命令:\nhelp - 显示帮助\nnfj <服务器序号(例:s1)> - 呼叫暖服机\nzc 使用方法:回复你要举报的图片的信息 zc <服务器序号> - 举报语言攻击(ai自动判别)"
    return help_text

# 新增函数：计算下一次提醒时间
def calculate_next_reminder_time(now, start_hour, start_min, end_hour, end_min, interval_min):
    start_time_dt = datetime.datetime.combine(now.date(), datetime.time(start_hour, start_min))
    end_time_dt = datetime.datetime.combine(now.date(), datetime.time(end_hour, end_min))
    if end_hour < start_hour or (end_hour == start_hour and end_min < start_min):
        end_time_dt += datetime.timedelta(days=1)  # 使 end_time 在次日
    interval_seconds = interval_min * 60
    if now < start_time_dt:
        next_time = start_time_dt
    else:
        time_since_start = (now - start_time_dt).total_seconds()
        intervals_passed = ceil(time_since_start / interval_seconds)
        next_time = start_time_dt + datetime.timedelta(seconds=intervals_passed * interval_seconds)
    if next_time > end_time_dt:
        # 跳到明天 start_time
        next_start = datetime.datetime.combine(now.date() + datetime.timedelta(days=1),
                                               datetime.time(start_hour, start_min))
        return next_start
    else:
        return next_time

# 新增异步函数：提醒循环任务
async def reminder_loop():
    config = NIUNIUBOT_CONFIG
    if not config.get("reminder", False):
        logger.info("提醒功能未启用，跳过提醒任务。")
        return
    # 获取提醒设置，默认值
    reminder_set = config.get("reminder_set", None)
    if reminder_set is None or not isinstance(reminder_set, dict):
        start_hour = 7
        start_min = 0
        end_hour = 1  # 凌晨1点
        end_min = 0
        interval_min = 30
    else:
        start_hour = reminder_set.get("start_hour", 7)
        start_min = reminder_set.get("start_min", 0)
        end_hour = reminder_set.get("end_hour", 1)
        end_min = reminder_set.get("end_min", 0)
        interval_min = reminder_set.get("interval_min", 30)
    logger.info(f"提醒任务启动：时间段从 {start_hour}:{start_min} 到 {end_hour}:{end_min}，间隔 {interval_min} 分钟。")
    while True:
        now = datetime.datetime.now()
        next_time = calculate_next_reminder_time(now, start_hour, start_min, end_hour, end_min, interval_min)
        wait_time = (next_time - now).total_seconds()
        await asyncio.sleep(wait_time)  # 等待到下一次提醒时间
        # 发送提醒（help 文本）
        help_text = get_help_text(config)
        group_ids = config.get("active_groups", [])
        if not group_ids:
            logger.warning("active_groups 配置为空，未发送提醒。")
            continue  # 如果没有群组，跳过发送
        for group_id in group_ids:
            try:
                await send_group_msg(group_id=group_id, text=help_text)
                logger.info(f"已发送提醒到群组 {group_id}")
            except Exception as e:
                logger.error(f"发送提醒到群组 {group_id} 失败: {str(e)}")

# 新增函数：异步提醒任务的线程函数
def run_reminder_in_thread():
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.run_until_complete(reminder_loop())

# 新增异步函数：使用 curl 和线程池下载图片
async def download_with_curl(image_url, image_path_wait):
    loop = asyncio.get_event_loop()
    try:
        # 使用 run_in_executor 在线程池中运行同步 subprocess.run
        result = await loop.run_in_executor(None, lambda: subprocess.run(
            ['curl', '-k', '-L', '-o', image_path_wait, image_url], check=True, timeout=60, stdout=subprocess.PIPE,
            stderr=subprocess.PIPE))
        if result.returncode == 0 and os.path.exists(image_path_wait) and os.path.getsize(image_path_wait) > 0:
            return True, "下载成功"
        else:
            error_msg = f"curl 下载失败: 返回码 {result.returncode}, 输出: {result.stdout.decode()}, 错误: {result.stderr.decode()}"
            return False, error_msg
    except subprocess.CalledProcessError as e:
        error_msg = f"子进程错误: {str(e)}，输出: {e.output.decode()}, 错误: {e.stderr.decode()}"
        return False, error_msg
    except subprocess.TimeoutExpired as e:
        error_msg = f"下载超时: {str(e)}"
        return False, error_msg
    except Exception as e:
        error_msg = f"未知错误: {str(e)}"
        return False, error_msg

# 新增异步函数：获取消息详情（基于OpenAPI规范，异步版本）
async def get_message_detail(message_id, config):
    base_url = config.get("address", "")
    access_token = config.get("token", "")  # token 可选，不再强制检查
    if not base_url:
        raise ValueError("API配置缺失，请检查config.json中的address")
    url = f"{base_url}/get_msg"
    payload = {"message_id": message_id}
    params_dict = {"access_token": access_token} if access_token else {}
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(url, json=payload, params=params_dict, timeout=10) as response:
                response.raise_for_status()
                data = await response.json()
                if data.get("status") == "ok":
                    return data.get("data", {})  # 返回消息详情
                else:
                    raise Exception(f"API错误: {data.get('message')}, 错误码: {data.get('retcode')}")
        except aiohttp.ClientError as e:
            raise Exception(f"网络请求失败: {str(e)}")
        except Exception as e:
            raise Exception(f"获取消息详情失败: {str(e)}")

def get_players_gametools(game_id=None):
    if game_id is None:
        return None
    url = f"https://api.gametools.network/bfv/players/?gameid={game_id}"
    max_retries = 3  # 最大重试次数
    timeout = 5  # 超时时间，单位：秒
    retry_delay = 1  # 重试之间的延迟时间，单位：秒
    for attempt in range(1, max_retries + 1):
        try:
            # 发送GET请求，并设置超时
            response = requests.get(url, timeout=timeout)
            if response.status_code == 200:
                # 如果成功，解析JSON数据
                data = response.json()
                return data  # 返回数据
            else:
                print(f"尝试 {attempt}: 请求失败，状态码: {response.status_code}")
                if attempt < max_retries:
                    print(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)  # 等待后再重试
        except requests.exceptions.RequestException as e:
            # 捕获请求异常（如超时、连接错误）
            print(f"尝试 {attempt}: 请求异常: {e}")
            if attempt < max_retries:
                print(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)  # 等待后再重试
    # 所有重试失败
    print("所有重试失败，无法获取数据。")
    return None  # 返回None表示失败

# 异步版本，使用 run_in_executor 运行同步函数
import asyncio
async def async_get_players_gametools(game_id=None):
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, get_players_gametools, game_id)

async def ai_judgment(image_url, from_who, player_list_str=None):
    if not image_url or not isinstance(image_url, str):
        logger.error("无效的image_url参数")
        return "判断失败: 无效的图像URL"
    if not isinstance(from_who, (int, str)):
        logger.error("无效的from_who参数")
        return "判断失败: 无效的用户ID"
    # 获取配置中的 AI 判断 URL
    ai_judgment_url = NIUNIUBOT_CONFIG.get("ai_judgement", "")
    if not ai_judgment_url:
        logger.error("AI判断URL配置缺失，请检查config.json中的'ai_judgement'")
        return "判断失败: AI配置不完整"
    # 准备请求数据
    payload = {
        "image_url": f"{image_url}",
        "from_who": f"{from_who}",
    }
    if player_list_str is not None and player_list_str != "":  # 只在 player_list_str 不为空时添加
        payload["player_list_str"] = player_list_str
    # 添加调试日志
    logger.info(f"发送AI判断请求到 {ai_judgment_url}，payload: {payload}")
    # 发送 POST 请求到外部 API，使用 aiohttp，并添加 User-Agent header
    max_retries = 5  # 最大重试次数，与原函数一致
    backoff_factor = 2  # 重试间隔因子
    headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"}  # 模拟浏览器 User-Agent
    for attempt in range(max_retries):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                        ai_judgment_url,
                        json=payload,
                        headers=headers,
                        timeout=60
                ) as response:
                    # 添加响应调试日志
                    response_text = await response.text()
                    logger.info(f"AI判断响应状态码: {response.status}, 响应文本: {response_text}")
                    response.raise_for_status()
                    result_json = await response.json()
                    if "error" in result_json:
                        return f"判断失败: {result_json.get('error')}"
                    else:
                        return result_json
        except aiohttp.ClientError as e:
            if attempt == max_retries - 1:
                logger.error(f"AI 判断失败 (尝试 {max_retries} 次): {str(e)}")
                return f"判断失败: {str(e)}"
            logger.warning(f"AI请求错误: {str(e)}，尝试 {attempt + 1}/{max_retries}")
            await asyncio.sleep(backoff_factor ** attempt)
    return "判断失败: AI服务多次请求失败，请稍后再试"

# 异步 check_rsp 函数（保持原样，仅替换配置变量）
async def check_rsp(msg_id, group_id=None):
    if not isinstance(msg_id, int):
        logger.error(f"msg_id '{msg_id}' 不是有效的整数，无法继续。")
        return [{"type": "text", "data": {"text": f"{msg_id}不是有效的整数"}}]
    message_array = []
    max_attempts = 6
    attempt = 0
    bot_qq = NIUNIUBOT_CONFIG.get("qq_bot", "")
    if not bot_qq:
        logger.error("qq_bot 配置缺失，请检查config.json")
        return [{"type": "text", "data": {"text": "配置错误: qq_bot 未设置"}}]
    while attempt < max_attempts:
        try:
            msg_data = await get_group_msg_history(group_id=group_id, message_seq=0, count=10)
            messages = msg_data.get('messages', [])
            found_target_msg = False
            subsequent_messages = []
            for msg in messages:
                if msg.get('message_id') == msg_id:
                    found_target_msg = True
                elif found_target_msg:
                    subsequent_messages.append(msg)
            if subsequent_messages:
                bot_data = await get_group_member_info(group_id=group_id, user_id=bot_qq)
                nickname = bot_data.get('nickname', '')
                for target_message in subsequent_messages:
                    if (f'[CQ:at,qq={bot_qq}]' in target_message.get('raw_message', '') or
                            any(item.get('type') == 'text' and f'@{nickname}' in item.get('data', {}).get('text', '')
                                for item in target_message.get('message', []))):
                        message_list = target_message.get('message', [])
                        for item in message_list:
                            item_type = item.get('type')
                            if item_type == 'image':
                                image_url = item['data'].get('url')
                                if image_url:
                                    message_array.append({"type": "image", "data": {"file": image_url}})
                            elif item_type == 'text':
                                text_content = item['data'].get('text', '')
                                cleaned_text = text_content.replace(f'@{nickname}', '')
                                urls = re.findall(r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+[/\w\.-]*(?:\?[\w=&]*)?',
                                                  cleaned_text)
                                if urls:
                                    for url in urls:
                                        message_array.append({"type": "url", "data": {"url": url}})
                                message_array.append({"type": "text", "data": {"text": cleaned_text}})
                            elif item_type == 'markdown':
                                markdown_content = item['data'].get('content', '')
                                image_urls = re.findall(r'!\[.*?\]\((https?://[^)]+)\)', markdown_content)
                                for url in image_urls:
                                    message_array.append({"type": "image", "data": {"file": url}})
                                text_content = re.sub(r'!\[.*?\]\(.*?\)|\[.*?\]\(.*?\)', '', markdown_content)
                                if text_content.strip():
                                    message_array.append({"type": "text", "data": {"text": text_content.strip()}})
                        if message_array:
                            logger.info(f"提取并转换的消息数组: {message_array}")
                            return message_array
            attempt += 1
            logger.info(f"第 {attempt} 次尝试未找到响应消息，等待3秒后重试...")
            await asyncio.sleep(3)
        except Exception as e:
            logger.error(f"check_rsp 错误: {str(e)}")
            attempt += 1
    logger.info("最大尝试次数已达上限，响应失败")
    message_array.append({"type": "text", "data": {"text": "响应失败"}})
    return message_array

# 异步 get_group_msg_history 函数（保持原样，仅替换配置变量）
async def get_group_msg_history(group_id=None, message_seq=None, count=None, reverseorder=False, access_token=None):
    base_url = NIUNIUBOT_CONFIG.get("address", "")
    if not base_url:
        raise ValueError("base_url 配置缺失，请检查config.json")
    group_id = group_id or NIUNIUBOT_CONFIG.get("public_groups", "")
    access_token = access_token or NIUNIUBOT_CONFIG.get("token", "")  # token 可选
    if not all([group_id, message_seq is not None, count is not None]):
        raise ValueError("group_id, message_seq, count 是必填参数")
    payload = {
        "group_id": str(group_id),
        "message_seq": str(message_seq),
        "count": count,
        "reverseOrder": reverseorder
    }
    params_dict = {"access_token": access_token} if access_token else {}
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(f"{base_url}/get_group_msg_history", json=payload, params=params_dict, timeout=10) as response:
                response.raise_for_status()
                data = await response.json()
                if data.get("status") == "ok":
                    return data.get("data", {})
                else:
                    raise Exception(f"API 错误: {data.get('message')}, 错误码: {data.get('retcode')}")
        except aiohttp.ClientError as e:
            raise Exception(f"网络请求失败: {str(e)}")

# 异步 get_group_member_info 函数（保持原样，仅替换配置变量）
async def get_group_member_info(group_id=None, user_id=None, access_token=None):
    base_url = NIUNIUBOT_CONFIG.get("address", "")
    if not base_url:
        raise ValueError("base_url 配置缺失，请检查config.json")
    group_id = group_id or NIUNIUBOT_CONFIG.get("nfj_group", "")
    user_id = user_id or NIUNIUBOT_CONFIG.get("qq_bot", "")
    access_token = access_token or NIUNIUBOT_CONFIG.get("token", "")  # token 可选
    if not all([group_id, user_id]):
        raise ValueError("group_id 和 user_id 是必填参数")
    payload = {"group_id": group_id, "user_id": user_id, "no_cache": True}
    params_dict = {"access_token": access_token} if access_token else {}
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(f"{base_url}/get_group_member_info", json=payload, params=params_dict, timeout=10) as response:
                response.raise_for_status()
                data = await response.json()
                if data.get("status") == "ok":
                    return data.get("data", {})
                else:
                    raise Exception(f"API 错误: {data.get('message')}, 错误码: {data.get('retcode')}")
        except aiohttp.ClientError as e:
            raise Exception(f"网络请求失败: {str(e)}")

# 异步 send_group_msg 函数（保持原样，仅替换配置变量）
async def send_group_msg(group_id=None, text=None, image_file=None, image_url=None, at_someone=None, reply_some=None, json_data=None, access_token=None):
    base_url = NIUNIUBOT_CONFIG.get("address", "")
    group_id_input = group_id or NIUNIUBOT_CONFIG.get("public_groups", "")  # 使用group_id_input避免冲突
    access_token = access_token or NIUNIUBOT_CONFIG.get("token", "")  # token 可选
    if not base_url or not group_id_input:  # 只检查base_url和group_id，移除对access_token的检查
        raise ValueError("必要配置或参数缺失，请检查config.json和输入")
    message_array = []
    if reply_some:
        message_array.append({"type": "reply", "data": {"id": reply_some}})
    if json_data:
        for item in json_data:
            if item.get("type") != "reply":
                message_array.append(item)
    if at_someone:
        message_array.append({"type": "at", "data": {"qq": at_someone}})
    if text:
        message_array.append({"type": "text", "data": {"text": f" {text}"}})
    if image_file:
        abs_image_file = os.path.abspath(image_file)
        if not os.path.exists(abs_image_file):
            raise FileNotFoundError(f"图片文件不存在: {abs_image_file}")
        message_array.append({"type": "image", "data": {"file": f"file://{abs_image_file}"}})
    if image_url:
        message_array.append({"type": "image", "data": {"file": image_url}})
    if not message_array:
        raise ValueError("没有提供任何消息内容，请至少提供文本或图片")
    payload = {"group_id": str(group_id_input), "message": message_array}
    params_dict = {"access_token": access_token} if access_token else {}
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(f"{base_url}/send_group_msg", json=payload, params=params_dict, timeout=10) as response:
                response.raise_for_status()
                data = await response.json()
                if data.get("status") == "ok":
                    return data["data"].get("message_id")
                else:
                    raise Exception(f"API 错误: {data.get('message')}, 错误码: {data.get('retcode')}")
        except aiohttp.ClientError as e:
            logger.error(f"发送群消息失败: {str(e)}")
            return None

# 异步 send_private_msg 函数（保持原样，仅替换配置变量）
async def send_private_msg(user_id, text):
    address = NIUNIUBOT_CONFIG.get("address", "")
    token = NIUNIUBOT_CONFIG.get("token", "")  # token 可选
    url = f"{address}/send_private_msg" if address else ""
    if not url or not user_id or not text:  # 只检查url, user_id, text，移除对token的检查
        logger.error("必要参数缺失")
        return None
    data = {"user_id": user_id, "message": text}
    params_dict = {"access_token": token} if token else {}
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(url, json=data, params=params_dict, timeout=10) as response:
                response.raise_for_status()
                return await response.json()
        except aiohttp.ClientError as e:
            logger.error(f"发送私聊消息失败: {e}")
            return None

# 修改后的 handle_message 函数：使用 message 数组解析命令
async def handle_message(message_data):
    try:
        message_type = message_data.get("message_type")
        if message_type == "group":
            group_id = message_data.get("group_id")
            if group_id not in NIUNIUBOT_CONFIG.get("active_groups", []):
                return
            # 解析 message 数组，提取所有文本内容
            message_array = message_data.get("message", [])
            raw_text = ""
            for item in message_array:
                if item.get("type") == "text":
                    raw_text += item["data"].get("text", "").strip()  # 提取文本部分，并去除多余空格
            # 现在 raw_text 包含所有文本，split 命令
            command_parts = raw_text.split(maxsplit=1)  # maxsplit=1，确保命令和参数正确分隔
            if command_parts:
                command = command_parts[0].lower()
                args = command_parts[1] if len(command_parts) > 1 else ""
                user_id = message_data.get("user_id")
                if command in command_handlers:
                    await command_handlers[command](group_id, user_id, args, is_private=False, message_data=message_data)
        elif message_type == "private":
            user_id = message_data.get("user_id")
            if user_id != NIUNIUBOT_CONFIG.get("admin_qq"):
                return
            message_array = message_data.get("message", [])
            raw_text = ""
            for item in message_array:
                if item.get("type") == "text":
                    raw_text += item["data"].get("text", "").strip()
            command_parts = raw_text.split(maxsplit=1)
            if command_parts:
                command = command_parts[0].lower()
                args = command_parts[1] if len(command_parts) > 1 else ""
                if command in command_handlers:
                    await command_handlers[command](None, user_id, args, is_private=True, message_data=message_data)
    except Exception as e:
        logger.error(f"处理消息失败: {str(e)}")

# 注册命令处理函数，改为异步，并使用 get_help_text
@register_command("help")
async def cmd_help(group_id, user_id, args, is_private=False, message_data=None):
    help_text = get_help_text(NIUNIUBOT_CONFIG)
    try:
        if is_private:
            await send_private_msg(user_id, help_text)
        else:
            await send_group_msg(group_id, text=help_text, at_someone=user_id)
    except Exception as e:
        logger.error(f"help 命令执行失败: {str(e)}")

is_nfj_using = False  # 标志位，表示暖服机指令是否正在使用

@register_command("nfj")
async def cmd_nfj(group_id, user_id, args, is_private=False, message_data=None):
    try:
        # 声明全局变量，确保可以修改
        global is_nfj_using
        if not args:
            await send_group_msg(group_id, at_someone=user_id, text="请提供要叫暖服机的服务器序号")
            return
        if is_nfj_using:
            await send_group_msg(group_id, at_someone=user_id, text="暖服机指令正在转发，请稍等一会再叫暖服机")
            return
        args_lower = args.lower().strip()
        # 获取服务器配置
        our_server_config = NIUNIUBOT_CONFIG.get("our_server", {})
        if not isinstance(our_server_config, dict):
            logger.error("our_server 配置无效或缺失，请检查config.json")
            await send_group_msg(group_id, at_someone=user_id, text="系统错误: 服务器配置无效，请联系管理员。")
            return
        if args_lower not in our_server_config:
            available_servers = ', '.join(our_server_config.keys())
            error_message = f"请提供正确的服务器序号，当前的服务器有：{available_servers}"
            await send_group_msg(group_id, at_someone=user_id, text=error_message)
            return
        # 设置标志位为 True，防止并发调用
        is_nfj_using = True
        # 发送消息
        #await send_group_msg(group_id, at_someone=user_id, text="暖服机命令还未启用，尽情期待...目前为查询服务器列表测试")
        # 检查 nfj_group 配置是否存在
        nfj_group_id = NIUNIUBOT_CONFIG.get("nfj_group", None)
        nfj_bot = NIUNIUBOT_CONFIG.get("nfj_bot", None)
        if not nfj_group_id and not nfj_bot:
            logger.error("nfj_group 或 nfj_bot 配置缺失，请检查config.json")
            await send_group_msg(group_id, at_someone=user_id, text="系统错误: nfj_group 配置无效，请联系管理员。")
            return
        if not NIUNIUBOT_CONFIG["nfj_on"]:
            await send_group_msg(group_id,text="暖服机指令未启用，正在呼叫管理员为你叫暖服机")
            await send_group_msg(group_id=nfj_group_id, text=f"有群成员需要暖服机 /warm 2636 {args}")
            return
        # 发送消息到指定群组
        send_msg_id = await send_group_msg(group_id=nfj_group_id, text=f"/warm 2636 {args}", at_someone=nfj_bot)
        rsp_data = await check_rsp(msg_id=send_msg_id, group_id=nfj_group_id)
        await send_group_msg(group_id, at_someone=user_id, json_data=rsp_data)
    except Exception as e:
        logger.error(f"nfj 命令执行失败: {str(e)}")
        await send_group_msg(group_id, at_someone=user_id, text="命令执行失败，请联系管理员。")
    finally:
        is_nfj_using = False

# 新增辅助函数：从消息数组中提取图片URL
def extract_image_url_from_message(message_array):
    for item in message_array:
        if item.get("type") == "image":
            url = item.get("data", {}).get("url")
            if url:
                return url  # 返回第一个图片URL，假设只处理单张图片
    return None  # 如果没有图片，返回None

# 处理嘴臭举报命令 zc
@register_command("zc")
async def cmd_zc(group_id, user_id, args, is_private=False, message_data=None):
    try:
        if message_data is None:
            await send_group_msg(group_id, at_someone=user_id, text="消息数据缺失，无法处理举报")
            return
        # 解析 args，提取服务器号（例如 "s1"）
        args_parts = args.strip().split()  # 分割 args，假设用户输入 "zc s1"，args 为 "s1"
        if len(args_parts) < 1:
            await send_group_msg(group_id, at_someone=user_id, text="请提供服务器号（例如 zc s1），并确保消息包含图片或回复图片消息")
            return
        server_key = args_parts[0].lower()  # 获取服务器号，如 "s1"
        # 检查服务器号是否有效
        our_server_config = NIUNIUBOT_CONFIG.get("our_server", {})
        if not isinstance(our_server_config, dict) or server_key not in our_server_config:
            available_servers = ', '.join(our_server_config.keys()) if our_server_config else "无"
            await send_group_msg(group_id, at_someone=user_id, text=f"无效的服务器号。当前可用服务器: {available_servers}")
            return
        game_id = our_server_config[server_key]  # 获取对应的 game_id
        # 获取玩家数据，使用异步版本
        players_data = await async_get_players_gametools(game_id=game_id)
        player_list_str = None
        if players_data:
            # 提取玩家名单，格式化为字符串
            player_names = []
            for team in players_data.get("teams", []):
                for player in team.get("players", []):
                    player_names.append(player.get("name", "未知玩家"))
            player_list_str = ", ".join(player_names)  # 例如 "baozaodemumu77, NiangZai, ..."
        # 查找消息中的图片或回复
        message_array = message_data.get("message", [])
        reply_id = None
        image_url = None
        for item in message_array:
            if item.get("type") == "reply":
                reply_id = item["data"].get("id")  # 提取回复ID
            elif item.get("type") == "image":
                image_url = item["data"].get("url")  # 提取图片URL
        if reply_id:
            # 有回复元素，获取被回复消息详情
            logger.info(f"检测到回复消息，目标消息ID: {reply_id}")
            message_detail = await get_message_detail(message_id=reply_id, config=NIUNIUBOT_CONFIG)
            detail_message_array = message_detail.get("message", [])
            image_url = extract_image_url_from_message(detail_message_array)
            if not image_url:
                await send_group_msg(group_id, at_someone=user_id, text="被回复的消息中未找到图片，无法完成举报")
                return
        elif not image_url:
            # 没有回复也没有图片
            await send_group_msg(group_id, at_someone=user_id, text="请提供证据（单张图片），或回复包含图片的消息")
            return
        # 发送处理确认消息
        await send_group_msg(group_id, at_someone=user_id, text=f"已接收到举报，正在处理服务器 {server_key} 的玩家数据...")
        # 调用 AI 判断，传递玩家名单
        judgment_result = await ai_judgment(image_url=image_url, from_who=user_id, player_list_str=player_list_str)
        if isinstance(judgment_result, dict):
            judgment = judgment_result.get("judgment", "提取失败")
            player = judgment_result.get("player", "提取失败")
            if judgment == "1":
                if not player or player == "提取失败":
                    await send_group_msg(group_id, at_someone=user_id, text="检测到可能有语言攻击，但提取信息失败，请联系管理员检查")
                    return
                await send_group_msg(group_id, at_someone=user_id, text="检测到有语言攻击，正在踢出")
                logger.info(f"踢人命令发送: /ban {player}，由用户 {user_id} 举报，服务器: {server_key}")
                await send_group_msg(group_id, at_someone="3889013937", text=f" /ban {player} 语言攻击")
                await send_group_msg(NIUNIUBOT_CONFIG["ban_group"], text=f"已在2636群组屏蔽{player}", image_url=image_url)
            else:
                await send_group_msg(group_id, at_someone=user_id, text="未检测到有语言攻击，如有异议请联系管理员")
        elif isinstance(judgment_result, str) and "失败" in judgment_result:
            await send_group_msg(group_id, at_someone=user_id, text=f"处理失败: {judgment_result}. 请重试或联系管理员。")
        else:
            await send_group_msg(group_id, at_someone=user_id, text="处理结果异常，请联系管理员")
    except Exception as e:
        logger.error(f"zc 命令执行失败: {str(e)}")
        await send_group_msg(group_id, at_someone=user_id, text="命令执行失败，请联系管理员")

# HTTP回调接口，使用 asyncio.run 执行异步 handle_message
@app.route('/api/event/post', methods=['POST'])
def event_post():
    try:
        data = request.get_json()
        logger.info(f"收到事件: {data}")
        if data.get("post_type") == "message":
            asyncio.run(handle_message(data))
            return jsonify({"status": "ok, processing in background"})
        return jsonify({"status": "ok"})
    except Exception as e:
        logger.error(f"处理事件失败: {e}")
        return jsonify({"status": "failed", "error": str(e)})

# 启动通知函数
def send_startup_notification():
    logger.info("机器人服务已启动")

if __name__ == '__main__':
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="NiuNiuBot Server")
    parser.add_argument('-b', '--bind', default='127.0.0.1', help='绑定地址，默认127.0.0.1')
    parser.add_argument('-p', '--port', type=int, default=18889, help='监听端口，默认18889')
    args = parser.parse_args()
    logger.info(f"使用绑定地址: {args.bind}, 端口: {args.port}")
    logger.info("正在启动机器人服务...")
    # 启动提醒任务线程，如果启用
    if NIUNIUBOT_CONFIG.get("reminder", False):
        reminder_thread = threading.Thread(target=run_reminder_in_thread)
        reminder_thread.daemon = True
        reminder_thread.start()
    # 启动通知线程
    notification_thread = threading.Thread(target=send_startup_notification)
    notification_thread.daemon = True
    notification_thread.start()
    # 启动Flask服务器，启用多线程模式
    app.run(host=args.bind, port=args.port, threaded=True)