import logging
import time
import requests
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
import math  # 用于 floor 或整数除法

logger = logging.getLogger(__name__)
# 配置日志级别，便于调试（可根据需要调整）
logging.basicConfig(level=logging.INFO)  # 设置为INFO级别，DEBUG级别可用于更详细输出


def get_players_gametools(game_id=None):
    if game_id is None:
        return None
    url = f"https://api.gametools.network/bfv/players/?gameid={game_id}"
    max_retries = 3  # 最大重试次数
    timeout = 5  # 超时时间，单位：秒
    retry_delay = 1  # 重试之间的延迟时间，单位：秒
    for attempt in range(1, max_retries + 1):
        try:
            # 发送GET请求，并设置超时
            response = requests.get(url, timeout=timeout)
            if response.status_code == 200:
                # 如果成功，解析JSON数据
                data = response.json()
                return data  # 返回数据
            else:
                logger.error(f"尝试 {attempt}: 服务器玩家列表请求失败，状态码: {response.status_code}")
                if attempt < max_retries:
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)  # 等待后再重试
        except requests.exceptions.RequestException as e:
            # 捕获请求异常（如超时、连接错误）
            logger.error(f"尝试 {attempt}: 服务器玩家列表请求异常: {e}")
            if attempt < max_retries:
                logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)  # 等待后再重试
    # 所有重试失败
    logger.error("服务器玩家列表所有重试失败，无法获取数据。")
    return None


def get_player_info(name=None, player_id=None):
    if name is None and player_id is None:
        return None
    if player_id:
        url = f"https://api.gametools.network/bfv/stats/?playerid={player_id}"
    else:
        url = f"https://api.gametools.network/bfv/stats/?name={name}"
    max_retries = 3  # 最大重试次数
    timeout = 5  # 超时时间，单位：秒
    retry_delay = 1  # 重试之间的延迟时间，单位：秒
    for attempt in range(1, max_retries + 1):
        try:
            # 发送GET请求，并设置超时
            response = requests.get(url, timeout=timeout)
            if response.status_code == 200:
                # 如果成功，解析JSON数据
                data = response.json()
                return data  # 返回数据
            else:
                logger.error(f"尝试 {attempt}: 玩家信息请求失败，状态码: {response.status_code}")
                if attempt < max_retries:
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)  # 等待后再重试
        except requests.exceptions.RequestException as e:
            # 捕获请求异常（如超时、连接错误）
            logger.error(f"尝试 {attempt}: 玩家信息请求异常: {e}")
            if attempt < max_retries:
                logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)  # 等待后再重试
    # 所有重试失败
    logger.error("玩家信息所有重试失败，无法获取数据。")
    return None


# 新增函数：计算单个玩家的基础评分（不包括权重）
def calculate_base_skill_score(player_info):
    if not player_info:
        return 0  # 如果玩家信息为空，返回0
    rank = player_info.get('rank', 0)
    seconds_played = player_info.get('secondsPlayed', 0)
    time_played_hours = min(seconds_played / 3600.0, 500.0)  # 转换为小时，并capping在500
    kill_death = player_info.get('killDeath', 0.0)
    kills_per_minute = player_info.get('killsPerMinute', 0.0)
    # 基础评分：简单平均，调整killDeath和killsPerMinute的权重为10
    base_score = (time_played_hours + rank) * kills_per_minute  # 基础评分公式
    return base_score


# 新增函数：计算加权评分（应用层级权重）
def calculate_weighted_skill_score(player_info):
    if not player_info:
        return 0
    rank = player_info.get('rank', 0)
    level = (rank // 100) + 1  # 计算层级
    weight = level * 0.5  # 权重公式：层级 * 0.5，可根据需要调整
    base_score = calculate_base_skill_score(player_info)  # 计算基础评分
    weighted_score = base_score * weight  # 应用权重
    return weighted_score


# 新增辅助函数：计算玩家层级分布和层级评分（每100级一个层级）
def calculate_rank_distribution_and_score(team_info, team_name):
    distribution = {}
    level_scores = {}  # 存储每个层级的总评分和玩家数，用于计算平均评分
    max_rank = 0
    for _, player_info, _ in team_info:  # team_info 是 [(player, info, skill_score), ...] 列表，但我们使用 player_info 计算
        rank = player_info.get('rank', 0)
        level = (rank // 100) + 1  # 计算层级
        if rank > max_rank:
            max_rank = rank
        weighted_score = calculate_weighted_skill_score(player_info)  # 计算加权评分
        if level not in distribution:
            distribution[level] = 0  # 玩家数量
            level_scores[level] = {'total_score': 0, 'count': 0}
        distribution[level] += 1
        level_scores[level]['total_score'] += weighted_score
        level_scores[level]['count'] += 1
    max_level = (max_rank // 100) + 1  # 计算最大层级
    # 计算每个层级的平均评分
    level_avg_scores = {}
    for level in range(1, max_level + 1):
        if level in level_scores and level_scores[level]['count'] > 0:
            avg_score = level_scores[level]['total_score'] / level_scores[level]['count']
        else:
            avg_score = 0  # 如果层级无玩家，评分设为0
        level_avg_scores[level] = avg_score
    return distribution, level_avg_scores, max_level


# 新增辅助函数：并发获取玩家信息
def fetch_player_info_concurrently(players):
    player_list = []
    if not players:
        logger.debug("无玩家需要获取信息，跳过并发处理")
        return player_list
    logger.info(f"开始并发获取 {len(players)} 个玩家的信息")
    with ThreadPoolExecutor(max_workers=10) as executor:  # 线程池大小可调整，max_workers=10 作为默认
        future_to_player = {executor.submit(get_player_info, player_id=player['player_id']): player for player in
                            players}
        for future in as_completed(future_to_player):
            player = future_to_player[future]
            try:
                info = future.result()  # 获取结果，阻塞直到完成
                logger.debug(f"完成玩家ID {player['player_id']} 的信息获取")
                if info:
                    # 这里计算加权评分，存储在 player_list 中
                    weighted_score = calculate_weighted_skill_score(info)
                    player_list.append((player, info, weighted_score))  # 存储玩家数据、info和加权评分
                else:
                    logger.warning(f"无法获取玩家ID {player['player_id']} 的详细统计信息，跳过该玩家")
            except Exception as e:
                logger.error(f"获取玩家ID {player['player_id']} 信息时发生错误: {e}")
    logger.info(f"并发获取玩家信息完成，共获取 {len(player_list)} 个有效玩家数据")
    return player_list


# 更新函数：平衡团队，决定踢掉哪些玩家（动态判断阵营，并使用并发获取玩家信息）
def balance_teams(players_data):
    logger.info("开始平衡团队计算")
    if not players_data or 'teams' not in players_data:
        logger.error("无效的玩家数据，无法进行平衡计算")
        logger.info("结束平衡团队计算")
        return {'kick_list': [], 'summary_text': "无效的玩家数据，无法进行平衡计算。"}

    teams = players_data['teams']
    if len(teams) != 2:
        logger.error("团队数据不完整，应有且仅有两支团队")
        logger.info("结束平衡团队计算")
        return {'kick_list': [], 'summary_text': "团队数据不完整，应有且仅有两支团队。"}

    # 动态判断进攻方（USA）和防守方（JPN），基于'key'字段
    attacker_team_data = None  # 进攻方，美军 (USA)
    defender_team_data = None  # 防守方，日军 (JPN)
    for team in teams:
        if team['key'] == 'USA_Pacific':  # 美军
            attacker_team_data = team
        elif team['key'] == 'JPN':  # 日军
            defender_team_data = team

    if not attacker_team_data or not defender_team_data:
        logger.error("无法识别进攻方或防守方团队，请检查'key'字段")
        logger.info("结束平衡团队计算")
        return {'kick_list': [], 'summary_text': "无法识别进攻方或防守方团队，请检查'key'字段。"}

    # 提取玩家数据
    attacker_players = attacker_team_data['players']
    defender_players = defender_team_data['players']

    # 使用并发获取玩家详细信息（包括加权评分）
    logger.info("开始并发获取攻击方玩家信息")
    attacker_info = fetch_player_info_concurrently(attacker_players)
    logger.info("完成攻击方玩家信息获取")

    # 计算攻击方层级分布、层级评分和最大层级
    attacker_distribution, attacker_level_avg_scores, attacker_max_level = calculate_rank_distribution_and_score(
        attacker_info, "USA")

    logger.info("开始并发获取防守方玩家信息")
    defender_info = fetch_player_info_concurrently(defender_players)
    logger.info("完成防守方玩家信息获取")

    # 计算防守方层级分布、层级评分和最大层级
    defender_distribution, defender_level_avg_scores, defender_max_level = calculate_rank_distribution_and_score(
        defender_info, "JPN")

    # 计算最大层级（取两队中较大的一个，确保覆盖所有层级）
    max_level = max(attacker_max_level, defender_max_level)

    # 计算每个团队的平均技能评分（使用加权评分）
    num_attacker = len(attacker_info)
    avg_attacker = sum(score for _, _, score in attacker_info) / num_attacker if num_attacker > 0 else 0
    num_defender = len(defender_info)
    avg_defender = sum(score for _, _, score in defender_info) / num_defender if num_defender > 0 else 0

    # 计算平均rank、timePlayed、killDeath和killsPerMinute（基础数据，未加权）
    if num_attacker > 0:
        avg_rank_attacker = sum(p_info['rank'] for _, p_info, _ in attacker_info) / num_attacker
        avg_time_played_attacker = sum(
            min(p_info['secondsPlayed'] / 3600.0, 1000.0) for _, p_info, _ in attacker_info) / num_attacker
        avg_kill_death_attacker = sum(p_info['killDeath'] for _, p_info, _ in attacker_info) / num_attacker
        avg_kpm_attacker = sum(p_info['killsPerMinute'] for _, p_info, _ in attacker_info) / num_attacker
    else:
        avg_rank_attacker = 0
        avg_time_played_attacker = 0
        avg_kill_death_attacker = 0
        avg_kpm_attacker = 0

    if num_defender > 0:
        avg_rank_defender = sum(p_info['rank'] for _, p_info, _ in defender_info) / num_defender
        avg_time_played_defender = sum(
            min(p_info['secondsPlayed'] / 3600.0, 500.0) for _, p_info, _ in defender_info) / num_defender
        avg_kill_death_defender = sum(p_info['killDeath'] for _, p_info, _ in defender_info) / num_defender
        avg_kpm_defender = sum(p_info['killsPerMinute'] for _, p_info, _ in defender_info) / num_defender
    else:
        avg_rank_defender = 0
        avg_time_played_defender = 0
        avg_kill_death_defender = 0
        avg_kpm_defender = 0

    # 构建总结文本（优化格式）
    summary_text = "=== 攻防玩家数据总结 ===\n"
    # 添加平均值对比
    summary_text += (
        "平均值对比(超过1000小时以1000小时计):\n"
        f"- 等级: JPN-{avg_rank_defender:.2f} USA-{avg_rank_attacker:.2f}\n"
        f"- 游戏时长: JPN-{avg_time_played_defender:.2f} 小时 USA-{avg_time_played_attacker:.2f} 小时\n"
        f"- KD: JPN-{avg_kill_death_defender:.2f} USA-{avg_kill_death_attacker:.2f}\n"
        f"- KPM: JPN-{avg_kpm_defender:.2f} USA-{avg_kpm_attacker:.2f}\n"
        f"- 评分: JPN-{avg_defender:.2f} USA-{avg_attacker:.2f}\n\n"
    )
    # 添加层级分布对比，包括层级评分
    summary_text += "层级分布对比 (每100级一个层级):\n"
    for level in range(1, max_level + 1):  # 从1到最大层级，连续遍历
        rank_range = f"{(level - 1) * 100}-{level * 100 - 1}"
        jpn_count = defender_distribution.get(level, 0)
        usa_count = attacker_distribution.get(level, 0)
        jpn_level_score = defender_level_avg_scores.get(level, 0)  # 层级平均加权评分，默认0
        usa_level_score = attacker_level_avg_scores.get(level, 0)  # 层级平均加权评分，默认0
        summary_text += f"- {rank_range}: JPN-{jpn_count} USA-{usa_count} 层级评分: JPN-{jpn_level_score:.2f} USA-{usa_level_score:.2f}\n"
    # 添加平衡检查和踢人建议
    # 平衡阈值，单位为技能评分差绝对值
    threshold = 50  # 可以根据需要调整阈值
    difference = avg_attacker - avg_defender
    if abs(difference) < threshold:
        logger.info("团队已平衡，无需踢人")
        kick_list = []
        summary_text += "\n团队已平衡，无需踢人。"
    else:
        # 确定哪支团队技能较高
        if difference > 0:
            stronger_team_info = attacker_info  # USA 评分较高
            weaker_avg = avg_defender
            stronger_faction = "USA (进攻方)"
        else:
            stronger_team_info = defender_info  # JPN 评分较高
            weaker_avg = avg_attacker
            stronger_faction = "JPN (防守方)"
            difference = -difference  # 使差值为正
        logger.info(f"{stronger_faction} 评分较高，当前评分差为 {difference:.2f}")
        # 检查是否踢掉一个人就能平衡
        can_balance_with_one = False
        for player_data, player_info, score in stronger_team_info:
            new_sum_skill = sum(s for _, _, s in stronger_team_info) - score
            new_num = len(stronger_team_info) - 1
            if new_num > 0:
                new_avg_stronger = new_sum_skill / new_num
                new_difference = abs(new_avg_stronger - weaker_avg)
                if new_difference < threshold:
                    can_balance_with_one = True
                    break
        if can_balance_with_one:
            logger.info("踢掉一个人就能平衡，但根据策略，不踢他，尝试踢多个玩家")
            sorted_stronger = sorted(stronger_team_info, key=lambda x: x[2], reverse=True)  # 按加权评分排序
            for k in range(2, len(sorted_stronger) + 1):  # k从2开始
                kicked_scores = [x[2] for x in sorted_stronger[:k]]
                sum_kicked_skill = sum(kicked_scores)
                new_sum_skill = sum(s for _, _, s in stronger_team_info) - sum_kicked_skill
                new_num = len(stronger_team_info) - k
                if new_num > 0:
                    new_avg_stronger = new_sum_skill / new_num
                    new_difference = abs(new_avg_stronger - weaker_avg)
                    if new_difference < threshold:
                        kick_names = [player['name'] for player, _, _ in sorted_stronger[:k]]
                        logger.info(f"建议踢掉以下{k}个玩家来平衡: {kick_names}")
                        kick_list = kick_names
                        summary_text += f"\n建议踢掉以下玩家来平衡: {', '.join(kick_list)}"
                        break
            else:
                # 如果无法通过多玩家平衡，踢掉评分最高的玩家
                kick_player_name = sorted_stronger[0][0]['name'] if sorted_stronger else "无可用玩家"
                logger.info(f"无法通过多玩家踢平衡，建议踢掉评分最高的玩家: {kick_player_name}")
                kick_list = [kick_player_name] if sorted_stronger else []
                summary_text += f"\n建议踢掉评分最高的玩家: {kick_list[0]}"
        else:
            # 无法通过踢一个人平衡，踢掉评分最高的玩家
            sorted_stronger = sorted(stronger_team_info, key=lambda x: x[2], reverse=True)
            kick_player_name = sorted_stronger[0][0]['name'] if sorted_stronger else "无可用玩家"
            logger.info(f"无法通过单人踢平衡，建议踢掉评分最高的玩家: {kick_player_name}")
            kick_list = [kick_player_name] if sorted_stronger else []
            summary_text += f"\n建议踢掉评分最高的玩家: {kick_list[0]}"
    logger.info("结束平衡团队计算")
    return {'kick_list': kick_list, 'summary_text': summary_text}


# 示例使用代码
if __name__ == "__main__":
    gameid = "10282519160010"  # 示例游戏ID
    players_data = get_players_gametools(gameid)
    if players_data:
        result = balance_teams(players_data)
        kick_list = result['kick_list']
        summary_text = result['summary_text']
        # 示例：打印总结文本
        print(summary_text)

        # 或保存到文件
        with open("team_summary.txt", "w", encoding="utf-8") as f:
            f.write(summary_text)

        if kick_list:
            print(f"建议踢掉的玩家: {', '.join(kick_list)}")
        else:
            print("团队已平衡或无需调整")
    else:
        print("无法获取玩家数据")