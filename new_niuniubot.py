import threading
import time
import os
import json
import re
import logging
import aiohttp  # 异步 HTTP 库
import requests
from flask import Flask, request, jsonify
import datetime
from math import ceil
import argparse
import logging.handlers
import asyncio

# 自定义日志配置
# ... (日志配置部分保持不变) ...
handler_file = logging.handlers.RotatingFileHandler('niuniubot.log', maxBytes=20*1024*1024, backupCount=5, encoding='utf-8')
handler_file.setLevel(logging.DEBUG)
handler_file.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
handler_console = logging.StreamHandler()
handler_console.setLevel(logging.INFO)
handler_console.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
root_logger = logging.getLogger()
root_logger.addHandler(handler_file)
root_logger.addHandler(handler_console)
root_logger.setLevel(logging.DEBUG)
werkzeug_logger = logging.getLogger("werkzeug")
werkzeug_logger.setLevel(logging.WARNING)
logger = logging.getLogger(__name__)


app = Flask(__name__)

# 加载配置从config.json文件
try:
    with open('config.json', 'r') as f:
        NIUNIUBOT_CONFIG = json.load(f)
    server_group_name = NIUNIUBOT_CONFIG.get("server_group_name", "默认群组名") # 添加默认值
    logger.info("NiuNiuBot配置加载成功")
except FileNotFoundError:
    logger.error("错误：config.json 文件未找到！请确保文件存在于当前目录。")
    NIUNIUBOT_CONFIG = {}
except json.JSONDecodeError:
    logger.error("错误：config.json 文件的JSON格式无效！请检查文件内容。")
    NIUNIUBOT_CONFIG = {}

# 新增全局变量，用于存储主 asyncio 事件循环
global_asyncio_loop = None
# 用于在主循环启动后发出信号
asyncio_loop_ready = threading.Event()
server_group_name = NIUNIUBOT_CONFIG.get("server_group_name", "默认群组名")

async def update_game_id():
    
    url = f"https://api.bfvrobot.net/api/bfv/servers?serverName={server_group_name}&region=all&limit=200&lang=zh-CN"
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                response.raise_for_status()
                data = await response.json()
                if data.get('success') != 1:
                    logger.error(f"game_id API请求失败: {data.get('code')}")
                    return
                servers_data = data.get('data', [])
                if not isinstance(servers_data, list):
                    logger.error("game_id API响应数据格式错误: 'data' 不是列表")
                    return
    except aiohttp.ClientError as e:
        logger.error(f"game_id 请求API失败: {str(e)}", exc_info=True)
        return
    except json.JSONDecodeError as e:
        logger.error(f"game_id 解析API响应JSON失败: {str(e)}", exc_info=True)
        return

    try:
        # 确保在更新配置时使用文件锁，因为主线程（Flask启动前）和 asyncio 线程都可能加载配置
        with file_lock: # 使用文件锁
            with open('config.json', 'r', encoding='utf-8') as f: # 指定编码
                config = json.load(f)
            logger.info("config.json 加载成功")
    except FileNotFoundError:
        logger.error("错误：config.json 文件未找到！")
        return
    except json.JSONDecodeError:
        logger.error("错误：config.json 文件的JSON格式无效！使用空配置。")
        return

    our_server_config = config.get('our_server', {})
    if not isinstance(our_server_config, dict):
        logger.warning("our_server 不是字典格式，创建空字典。")
        our_server_config = {}

    updated = False
    for key in list(our_server_config.keys()):
        key_lower = key.lower()
        matching_server = None
        for server in servers_data:
            if isinstance(server, dict):
                server_name_lower = server.get('serverName', '').lower()
                if key_lower in server_name_lower:
                    matching_server = server
                    break
        if matching_server:
            new_game_id = matching_server.get('gameId')
            if new_game_id is not None:
                old_game_id = our_server_config.get(key)
                if str(new_game_id) != str(old_game_id):
                    our_server_config[key] = str(new_game_id)
                    logger.info(f"更新 {key} 的 gameId: {old_game_id} -> {new_game_id}")
                    updated = True
                else:
                    logger.info(f"{key} 的 gameId 未变化: {new_game_id}")
            else:
                logger.warning(f"服务器匹配到 {key} 但缺少 gameId 字段")
        else:
            logger.warning(f"未找到匹配的服务器: {key}")

    if updated:
        config['our_server'] = our_server_config
        try:
            with file_lock: # 使用文件锁
                with open('config.json', 'w', encoding='utf-8') as f: # 指定编码
                    json.dump(config, f, indent=4)
            logger.info("config.json 更新成功")
        except IOError as e:
            logger.error(f"保存 config.json 失败: {str(e)}")

    # 重新加载配置到 NIUNIUBOT_CONFIG
    # 确保在重新加载时使用文件锁
    try:
        with file_lock: # 使用文件锁
            with open('config.json', 'r', encoding='utf-8') as f: # 指定编码
                global NIUNIUBOT_CONFIG
                NIUNIUBOT_CONFIG = json.load(f)
        logger.info("NiuNiuBot 配置重新加载成功")
        # 重新设置全局的 server_group_name
    except FileNotFoundError:
        logger.error("错误：config.json 文件未找到！")
        NIUNIUBOT_CONFIG = {}
    except json.JSONDecodeError:
        logger.error("错误：config.json JSON格式无效！")
        NIUNIUBOT_CONFIG = {}


# 定义一个函数来打印配置为表格格式
def print_config_table(config):
     
     if not config:
         return "无配置可用。"
     # Handle cases where config might not be a dict
     if not isinstance(config, dict):
         return "配置不是字典格式。"

     max_key_length = 0
     # Safely iterate keys, handling non-string keys if necessary
     for key in config.keys():
         max_key_length = max(max_key_length, len(str(key)))

     table_str = f"{'Key'.ljust(max_key_length)} | Value\n"
     table_str += "-" * (max_key_length + 7) + "\n"

     for key, value in config.items():
         value_str = str(value)
         # Ensure value_str doesn't contain newlines that break the table format
         value_str = value_str.replace('\n', '\\n')
         table_str += f"{str(key).ljust(max_key_length)} | {value_str}\n"
     return table_str


# 打印配置信息为表格格式 (在加载/重新加载后调用)
logger.info("当前的NiuNiuBot配置:\n" + print_config_table(NIUNIUBOT_CONFIG))


# 创建全局锁，用于保护文件操作 (如config.json)
file_lock = threading.Lock()

DEFAULT_HELP_TEXT = "可用命令:\n显示帮助:nhelp\n呼叫暖服机命令:nfj 服务器序号(例:s1)\n举报语言攻击命令(ai自动判别):使用方法:回复你要举报的图片的信息 zc 服务器序号\n查看服务器列表 2"

def get_help_text(config):
     
     our_server_config = config.get("our_server", {})
     if not isinstance(our_server_config, dict):
         available_servers = "无"
     else:
         available_servers = ', '.join(our_server_config.keys())

     try:
         # 确保在读取文件时使用文件锁
         with file_lock:
             with open('help.txt', 'r', encoding='utf-8') as file:
                 help_text = file.read().strip()
     except FileNotFoundError:
         logging.error("help.txt 文件未找到，请检查文件是否存在。")
         help_text = DEFAULT_HELP_TEXT
     except UnicodeDecodeError:
         logging.error("help.txt 文件编码错误，无法正确读取。")
         help_text = DEFAULT_HELP_TEXT
     except Exception as e:
         logging.error(f"读取help.txt文件时发生错误: {str(e)}")
         help_text = DEFAULT_HELP_TEXT

     full_help_text = f"欢迎来到{server_group_name}群组\n当前已有的服务器:{available_servers}\n{help_text}"
     return full_help_text

# 新增函数：计算下一次提醒时间
def calculate_next_reminder_time(now, start_hour, start_min, end_hour, end_min, interval_min):
    
    start_time_dt = datetime.datetime.combine(now.date(), datetime.time(start_hour, start_min))
    end_time_dt = datetime.datetime.combine(now.date(), datetime.time(end_hour, end_min))
    if end_hour < start_hour or (end_hour == start_hour and end_min < start_min):
        end_time_dt += datetime.timedelta(days=1)
    interval_seconds = interval_min * 60
    if now < start_time_dt:
        next_time = start_time_dt
    else:
        time_since_start = (now - start_time_dt).total_seconds()
        # Ensure interval_seconds is not zero or negative
        if interval_seconds <= 0:
             logger.error(f"提醒间隔设置无效: {interval_min}分钟")
             # Return a time far in the future to effectively disable this reminder
             return now + datetime.timedelta(days=365) # Return a time one year later

        intervals_passed = ceil(time_since_start / interval_seconds)
        next_time = start_time_dt + datetime.timedelta(seconds=intervals_passed * interval_seconds)

    # If next_time is *exactly* the end time, it should be the last reminder for the day.
    # If it's after the end time, calculate for the next day.
    # Using a small tolerance for floating point comparison is safer.
    if next_time > end_time_dt + datetime.timedelta(seconds=1): # Add a small tolerance
        next_start = datetime.datetime.combine(now.date() + datetime.timedelta(days=1),
                                               datetime.time(start_hour, start_min))
        return next_start
    else:
        return next_time

# 新增异步函数：提醒循环任务
async def reminder_loop():
    # Ensure this task runs on the global loop
    logger.info("提醒任务启动。")
    await update_game_id() # Initial update on startup

    while True:
        # Ensure configuration is accessed safely if it can change
        config = NIUNIUBOT_CONFIG # Assuming config is loaded and updated globally

        if not config.get("reminder", False):
            logger.info("提醒功能未启用，暂停提醒任务。")
            # If reminder is disabled, sleep for a longer period before checking config again
            await asyncio.sleep(300) # Check config every 5 minutes
            continue # Go back to the start of the loop

        # Get reminder settings safely, providing defaults
        reminder_set = config.get("reminder_set", {}) # Default to empty dict
        if not isinstance(reminder_set, dict):
            logger.error("reminder_set 配置无效，使用默认提醒时间。")
            reminder_set = {} # Use defaults below

        start_hour = reminder_set.get("start_hour", 7)
        start_min = reminder_set.get("start_min", 1)
        end_hour = reminder_set.get("end_hour", 1)
        end_min = reminder_set.get("end_min", 1)
        interval_min = reminder_set.get("interval_min", 60)

        # Basic validation for time and interval
        if not (0 <= start_hour < 24 and 0 <= start_min < 60 and
                0 <= end_hour < 24 and 0 <= end_min < 60 and
                interval_min > 0):
             logger.error(f"提醒时间或间隔配置无效：start={start_hour}:{start_min}, end={end_hour}:{end_min}, interval={interval_min}分钟。暂停提醒。")
             await asyncio.sleep(300) # Wait before checking config again
             continue

        now = datetime.datetime.now()
        next_time = calculate_next_reminder_time(now, start_hour, start_min, end_hour, end_min, interval_min)

        wait_time = (next_time - now).total_seconds()
        # Ensure wait_time is not negative (can happen if clock changes or calc error)
        if wait_time < 0:
            wait_time = 1 # Wait at least 1 second if time is in the past

        logger.info(f"下一次提醒时间: {next_time.strftime('%Y-%m-%d %H:%M:%S')} (等待 {int(wait_time)} 秒)")

        try:
            # Use asyncio.sleep for waiting within an async function
            await asyncio.sleep(wait_time)
        except asyncio.CancelledError:
            logger.info("提醒任务被取消。")
            raise # Re-raise the exception to let the caller know

        # Ensure the current time is within the reminder window before sending
        # Recalculate now and check if it's within the window for sending
        current_check_time = datetime.datetime.now()
        start_time_today = datetime.datetime.combine(current_check_time.date(), datetime.time(start_hour, start_min))
        end_time_today = datetime.datetime.combine(current_check_time.date(), datetime.time(end_hour, end_min))
        if end_hour < start_hour or (end_hour == start_hour and end_min < start_min):
            # End time is on the next day
            if current_check_time >= start_time_today or current_check_time <= end_time_today:
                 # Within the time window (either after start today or before end tomorrow)
                 send_reminder = True
            else:
                 send_reminder = False
        else:
            # End time is on the same day
            if start_time_today <= current_check_time <= end_time_today:
                 send_reminder = True
            else:
                 send_reminder = False

        if send_reminder:
             # 发送提醒（help 文本）
             help_text = get_help_text(config) # Get latest help text
             group_ids = config.get("reminder_groups", [])

             if not group_ids:
                 logger.warning("reminder_groups 配置为空，未发送提醒。")
                 # Continue to the next interval even if no groups
             else:
                 # Update game ID before sending reminders
                 await update_game_id()
                 # Send reminder to each group
                 for group_id in group_ids:
                     try:
                         await send_group_msg(group_id=group_id, text=help_text)
                         logger.info(f"已发送提醒到群组 {group_id}")
                     except Exception as e:
                         logger.error(f"发送提醒到群组 {group_id} 失败: {str(e)}")
        else:
             logger.info(f"当前时间 {current_check_time.strftime('%H:%M')} 不在提醒时间段内，跳过发送。")


# 新增函数：运行 asyncio 事件循环的线程函数
def run_asyncio_in_thread():
    # 在新线程中创建和设置新的事件循环
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    # 将循环存储到全局变量，供其他线程访问
    global global_asyncio_loop
    global_asyncio_loop = loop

    logger.info("Asyncio 事件循环线程启动。")

    # 启动主 asyncio 逻辑（包括后台任务，如提醒）
    # 使用 loop.run_until_complete 来运行一个顶级协程，这个协程可以启动其他任务
    async def main_asyncio_tasks():
        logger.info("主 asyncio 任务启动。")
        # 启动提醒任务作为后台任务
        if NIUNIUBOT_CONFIG.get("reminder", False):
             asyncio.create_task(reminder_loop())
             logger.info("提醒循环任务已在主循环中启动。")

        # Add a Future that will keep the loop running indefinitely until cancelled
        # This is necessary if other tasks might finish, but you want the loop alive
        # until the whole application shuts down.
        stop_event = asyncio.Event() # Use an event to signal shutdown
        asyncio_loop_ready.set() # Signal that the loop is ready

        try:
            # Wait until the stop_event is set (e.g., by a shutdown signal)
            await stop_event.wait()
        except asyncio.CancelledError:
            logger.info("主 asyncio 任务收到取消信号。")
        except Exception as e:
            logger.error(f"主 asyncio 任务发生未预期错误: {e}", exc_info=True)
        finally:
            logger.info("主 asyncio 任务正在关闭所有子任务...")
            # Cancel all running tasks except the one currently executing this finally block
            tasks = [t for t in asyncio.all_tasks(loop=loop) if t is not asyncio.current_task(loop=loop)]
            for task in tasks:
                 task.cancel()
            # Wait for tasks to finish or be cancelled
            await asyncio.gather(*tasks, return_exceptions=True)
            logger.info("所有 asyncio 子任务已关闭。")


    try:
        # 运行主 asyncio 任务，这将启动其他后台任务并保持循环运行
        loop.run_until_complete(main_asyncio_tasks())
    except asyncio.CancelledError:
        logger.info("Asyncio 循环线程被取消。")
    except Exception as e:
        logger.error(f"Asyncio 循环线程错误: {e}", exc_info=True)
    finally:
        logger.info("Asyncio 事件循环线程停止。")
        # 清理全局变量和循环
        global_asyncio_loop = None
        loop.close()
        logger.info("Asyncio 事件循环已关闭。")


# 新增异步函数：获取消息详情（基于OpenAPI规范，异步版本）
async def get_message_detail(message_id, config):
     
     base_url = config.get("address", "")
     access_token = config.get("token", "")
     if not base_url:
         raise ValueError("API配置缺失，请检查config.json中的address")
     url = f"{base_url}/get_msg"
     payload = {"message_id": message_id}
     params_dict = {"access_token": access_token} if access_token else {}
     async with aiohttp.ClientSession() as session:
         try:
             async with session.post(url, json=payload, params=params_dict, timeout=10) as response:
                 response.raise_for_status()
                 data = await response.json()
                 if data.get("status") == "ok":
                     return data.get("data", {})
                 else:
                     raise Exception(f"API错误: {data.get('message')}, 错误码: {data.get('retcode')}")
         except aiohttp.ClientError as e:
             raise Exception(f"网络请求失败: {str(e)}")
         except Exception as e:
             raise Exception(f"获取消息详情失败: {str(e)}")

# 异步版本，使用 run_in_executor 运行同步函数
async def async_get_players_gametools(game_id=None):
    # Get the currently running loop (should be the global_asyncio_loop in this thread)
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, get_players_gametools, game_id)

# 同步版本 get_players_gametools (保持不变)
def get_players_gametools(game_id=None):
     
     if game_id is None:
         logger.error("game_id 为空，无法获取玩家列表。")
         return None
     url = f"https://api.gametools.network/bfv/players/?gameid={game_id}"
     max_retries = 3
     timeout = 15  # 适当增加超时时间
     retry_delay = 2 # 适当增加重试延迟
     for attempt in range(1, max_retries + 1):
         try:
             # 发送GET请求，并设置超时
             response = requests.get(url, timeout=timeout)
             if response.status_code == 200:
                 # 如果成功，解析JSON数据
                 data = response.json()
                 # 简单检查数据结构
                 if isinstance(data, dict) and 'teams' in data:
                     return data
                 else:
                     logger.error(f"尝试 {attempt}: 服务器玩家列表API返回数据结构异常。")
                     return None # 返回 None 表示数据无效
             else:
                 logger.error(f"尝试 {attempt}: 服务器玩家列表请求失败，状态码: {response.status_code}")
                 if attempt < max_retries:
                     logger.info(f"等待 {retry_delay} 秒后重试...")
                     time.sleep(retry_delay)
         except requests.exceptions.RequestException as e:
             logger.error(f"尝试 {attempt}: 服务器玩家列表请求异常: {e}", exc_info=True)
             if attempt < max_retries:
                 logger.info(f"等待 {retry_delay} 秒后重试...")
                 time.sleep(retry_delay)
     logger.error("服务器玩家列表所有重试失败，无法获取数据。")
     return None


async def ai_judgment(image_url, from_who, player_list_str=None):
     
     if not image_url or not isinstance(image_url, str):
         logger.error("无效的image_url参数")
         return "判断失败: 无效的图像URL"
     # user_id 可以是 int 或 str
     if not isinstance(from_who, (int, str)):
         logger.error("无效的from_who参数")
         return "判断失败: 无效的用户ID"

     ai_judgment_url = NIUNIUBOT_CONFIG.get("ai_judgement", "")
     if not ai_judgment_url:
         logger.error("AI判断URL配置缺失，请检查config.json中的'ai_judgement'")
         return "判断失败: AI配置不完整"

     payload = {
         "image_url": image_url,
         "from_who": str(from_who), # Convert to string for consistent payload
     }
     if player_list_str is not None and player_list_str.strip() != "": # Check if non-empty string
         payload["player_list_str"] = player_list_str.strip() # Use stripped string

     max_retries = 5
     backoff_factor = 2
     headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"}

     for attempt in range(max_retries):
         try:
             async with aiohttp.ClientSession() as session:
                 # Adjust timeout for AI judgment if needed, 60s might be too short/long
                 timeout = aiohttp.ClientTimeout(total=90) # Example: 90 seconds timeout
                 async with session.post(
                         ai_judgment_url,
                         json=payload,
                         headers=headers,
                         timeout=timeout
                 ) as response:
                     logger.info(f"AI判断响应状态码: {response.status}")
                     response.raise_for_status()
                     result_json = await response.json()

                     # Check the specific structure expected from your AI service
                     if isinstance(result_json, dict) and "error" in result_json:
                          # Assuming 'error' key indicates failure from the AI side
                          return f"判断失败: {result_json.get('error', '未知错误')}"
                     elif isinstance(result_json, dict) and "judgment" in result_json:
                          # Assuming 'judgment' key indicates success
                          return result_json # Return the dictionary result
                     else:
                          # Unexpected successful response format
                          logger.error(f"AI 判断API返回未知格式: {result_json}")
                          return "判断失败: AI服务返回未知格式"

         except aiohttp.ClientResponseError as e:
             error_status = e.status
             error_message = e.message
             logger.error(f"AI请求 ClientResponseError (尝试 {attempt + 1}/{max_retries}): 状态码 {error_status}, 错误消息: {error_message}", exc_info=True)
             if attempt >= max_retries - 1:
                 return f"判断失败: 状态码 {error_status}, 错误: {error_message}"
             await asyncio.sleep(backoff_factor ** attempt)
         except aiohttp.ClientError as e:
             logger.error(f"AI请求 ClientError (尝试 {attempt + 1}/{max_retries}): {str(e)}", exc_info=True)
             if attempt >= max_retries - 1:
                 return f"判断失败: 网络错误或AI服务无响应: {str(e)}"
             await asyncio.sleep(backoff_factor ** attempt)
         except Exception as e:
             # Catch any other unexpected errors during the loop
             logger.error(f"AI判断请求中发生未预期异常 (尝试 {attempt + 1}/{max_retries}): {str(e)}", exc_info=True)
             if attempt >= max_retries - 1:
                  return f"判断失败: 处理结果时发生未知错误: {str(e)}"
             await asyncio.sleep(backoff_factor ** attempt)


     return "判断失败: AI服务多次请求失败，请稍后再试"

async def check_rsp(msg_id, group_id=None, match_condition=None):
     
     if not isinstance(msg_id, int):
         logger.error(f"check_rsp: msg_id '{msg_id}' 不是有效的整数，无法继续。")
         return [{"type": "text", "data": {"text": f"{msg_id}不是有效的整数"}}]

     config = NIUNIUBOT_CONFIG # Access config

     bot_qq = config.get("qq_bot", "")
     if not bot_qq:
         logger.error("check_rsp: qq_bot 配置缺失，请检查config.json")
         return [{"type": "text", "data": {"text": "配置错误: qq_bot 未设置"}}]

     card = ''
     try:
         # Use the global loop for waiting
         bot_data = await asyncio.wait_for(get_group_member_info(group_id=group_id, user_id=bot_qq), timeout=5)
         card = bot_data.get('card', '')
     except asyncio.TimeoutError:
         logger.error("check_rsp: get_group_member_info 调用超时，机器人昵称设置为默认空值")
     except Exception as e:
         logger.error(f"check_rsp: 获取机器人昵称失败: {str(e)}，机器人昵称设置为默认空值", exc_info=True)

     if match_condition is None:
         server_group_name_lower = config.get("server_group_name", "").lower() # Get server_group_name from config
         bot_qq_str = str(bot_qq) # Convert bot_qq to string for comparison

         def default_match(msg):
             raw_message_lower = msg.get('raw_message', '').lower()
             # Check for server_group_name (case-insensitive) or @bot
             # Using CQ code for @bot is more reliable than checking card/nickname in raw_message
             return (server_group_name_lower in raw_message_lower and server_group_name_lower != "") or \
                    (f'[cq:at,qq={bot_qq_str}]' in raw_message_lower or \
                     f'[cq:at,qq=-{bot_qq_str}]' in raw_message_lower) # Consider negative QQ for different bot types/clients?


         match_func = default_match
     else:
         match_func = match_condition

     max_attempts = 10
     attempt = 0
     while attempt < max_attempts:
         try:
             # Get message history, max 20 messages back from seq 0 (latest)
             # Use the global loop for waiting
             msg_data = await asyncio.wait_for(get_group_msg_history(group_id=group_id, message_seq=0, count=20),
                                               timeout=10) # Increase timeout slightly
             messages = msg_data.get('messages', [])

             found_target_msg = False
             for msg in messages:
                 # Ensure message_id is compared correctly (could be int or str)
                 if str(msg.get('message_id')) == str(msg_id):
                     found_target_msg = True
                     continue

                 if found_target_msg:
                     # Use match_func to check if the message is the response
                     if match_func(msg):
                         # Parse the message content if it matches
                         message_array = []
                         for item in msg.get('message', []):
                              item_type = item.get('type')
                              item_data = item.get('data', {})
                              if item_type == 'image':
                                   image_url = item_data.get('url')
                                   if image_url:
                                        message_array.append({"type": "image", "data": {"file": image_url}}) # Use "file" for URL in send_group_msg? Or "url"? check send_group_msg
                                        # send_group_msg uses "file" for local path and "file" for url in the example
                                        # Let's stick to "file" with url string for consistency with send_group_msg
                                        # message_array.append({"type": "image", "data": {"file": image_url}}) # Let's use 'url' key as per CQHTTP/go-cqhttp spec sometimes
                                        # Re-checking send_group_msg: it uses 'file' for both file path and url. Ok.
                                        message_array.append({"type": "image", "data": {"file": image_url}})
                              elif item_type == 'text':
                                   text_content = item_data.get('text', '')
                                   # Clean up @bot mention if card is available
                                   if card and f'@{card}' in text_content:
                                       # Use regex for more robust replacement, considering potential leading/trailing spaces
                                       cleaned_text = re.sub(rf'\s*@{re.escape(card)}\s*', '', text_content).strip()
                                   else:
                                       cleaned_text = text_content.strip() # Still strip leading/trailing spaces

                                   # Add cleaned text if not empty after stripping
                                   if cleaned_text:
                                       message_array.append({"type": "text", "data": {"text": cleaned_text + "\n"}}) # Removed the newline here, let the sender manage newlines

                              # Add other types if needed, e.g., face, reply etc.
                              # For now, just handle text and image as they seem relevant for responses

                         # Found the correct response message
                         logger.info(f"check_rsp: 找到并解析第一个响应消息: message_id {msg.get('message_id')}")
                         return message_array

             # If the loop finishes without finding a match after the target_msg
             attempt += 1
             if attempt < max_attempts:
                 logger.info(f"check_rsp: 第 {attempt} 次尝试未找到响应消息 (msg_id {msg_id})，等待3秒后重试...")
                 await asyncio.sleep(3)

         except asyncio.CancelledError:
             logger.warning(f"check_rsp: 任务被取消 while waiting for msg_id {msg_id}")
             raise # Re-raise cancellation

         except Exception as e:
             logger.error(f"check_rsp 错误 while waiting for msg_id {msg_id}: {str(e)}", exc_info=True)
             attempt += 1
             if attempt < max_attempts:
                 logger.info(f"check_rsp: 错误后等待3秒后重试...")
                 await asyncio.sleep(3)

     # Max attempts reached
     logger.info(f"check_rsp: 最大尝试次数已达上限，未找到响应消息 (msg_id {msg_id})。")
     return [{"type": "text", "data": {"text": f"\n{3*max_attempts}秒内未找到指定返回消息，响应失败\n"}}]


# 异步 get_group_msg_history 函数
async def get_group_msg_history(group_id=None, message_seq=None, count=None, reverseorder=False, access_token=None):
     
     base_url = NIUNIUBOT_CONFIG.get("address", "")
     if not base_url:
         raise ValueError("get_group_msg_history: base_url 配置缺失，请检查config.json")

     # Use provided group_id or config value
     group_id_to_use = group_id
     if group_id_to_use is None:
          # Fallback to public_groups if group_id is None, but public_groups might be a list
          # This function expects a single group_id. Let's use the first if it's a list.
          cfg_group = NIUNIUBOT_CONFIG.get("public_groups")
          if isinstance(cfg_group, list) and cfg_group:
               group_id_to_use = cfg_group[0]
          elif isinstance(cfg_group, (int, str)):
               group_id_to_use = cfg_group
          else:
               raise ValueError("get_group_msg_history: group_id 或 public_groups 配置无效")


     token_to_use = access_token or NIUNIUBOT_CONFIG.get("token", "") # token 可选

     if not all([group_id_to_use is not None, message_seq is not None, count is not None]):
         raise ValueError("get_group_msg_history: group_id, message_seq, count 是必填参数")

     payload = {
         "group_id": str(group_id_to_use), # Ensure group_id is string
         "message_seq": str(message_seq),   # Ensure seq is string
         "count": count,
         "reverseOrder": reverseorder
     }
     params_dict = {"access_token": token_to_use} if token_to_use else {}

     async with aiohttp.ClientSession() as session:
         try:
             # Adjust timeout if message history is large or connection is slow
             timeout = aiohttp.ClientTimeout(total=15) # Example: 15 seconds timeout
             async with session.post(f"{base_url}/get_group_msg_history", json=payload, params=params_dict, timeout=timeout) as response:
                 response.raise_for_status()
                 data = await response.json()
                 if data.get("status") == "ok":
                     return data.get("data", {})
                 else:
                     logger.error(f"get_group_msg_history API 错误: {data.get('message')}, 错误码: {data.get('retcode')}")
                     raise Exception(f"API 错误: {data.get('message')}, 错误码: {data.get('retcode')}")
         except aiohttp.ClientError as e:
             logger.error(f"get_group_msg_history 网络请求失败: {str(e)}", exc_info=True)
             raise Exception(f"网络请求失败: {str(e)}")
         except Exception as e:
             logger.error(f"get_group_msg_history 错误: {str(e)}", exc_info=True)
             raise Exception(f"get_group_msg_history 错误: {str(e)}")


# 异步 get_group_member_info 函数
async def get_group_member_info(group_id=None, user_id=None, access_token=None):
     
     base_url = NIUNIUBOT_CONFIG.get("address", "")
     if not base_url:
         raise ValueError("get_group_member_info: base_url 配置缺失，请检查config.json")

     # Use provided group_id/user_id or config values
     group_id_to_use = group_id or NIUNIUBOT_CONFIG.get("nfj_group") # Fallback for nfj_group
     user_id_to_use = user_id or NIUNIUBOT_CONFIG.get("qq_bot") # Fallback for qq_bot
     token_to_use = access_token or NIUNIUBOT_CONFIG.get("token", "") # token 可选

     if not all([group_id_to_use is not None, user_id_to_use is not None]):
         raise ValueError("get_group_member_info: group_id 和 user_id 是必填参数或配置项")

     payload = {"group_id": str(group_id_to_use), "user_id": str(user_id_to_use), "no_cache": True} # Ensure string types
     params_dict = {"access_token": token_to_use} if token_to_use else {}

     async with aiohttp.ClientSession() as session:
         try:
             timeout = aiohttp.ClientTimeout(total=10)
             async with session.post(f"{base_url}/get_group_member_info", json=payload, params=params_dict, timeout=timeout) as response:
                 response.raise_for_status()
                 data = await response.json()
                 if data.get("status") == "ok":
                     return data.get("data", {})
                 else:
                      logger.error(f"get_group_member_info API 错误: {data.get('message')}, 错误码: {data.get('retcode')}")
                      # Return empty dict or raise exception depending on desired behavior
                      return {} # Return empty dict on API error for gracefully handling missing info
         except aiohttp.ClientError as e:
             logger.error(f"get_group_member_info 网络请求失败: {str(e)}", exc_info=True)
             # Return empty dict on network error
             return {}
         except Exception as e:
             logger.error(f"get_group_member_info 错误: {str(e)}", exc_info=True)
             # Return empty dict on other errors
             return {}


# 异步 send_group_msg 函数
async def send_group_msg(group_id=None, text=None, image_file=None, image_url=None, at_someone=None, reply_some=None, json_data=None, access_token=None):
     
     base_url = NIUNIUBOT_CONFIG.get("address", "")
     if not base_url:
         logger.error("send_group_msg: base_url 配置缺失，请检查config.json")
         return None # Return None or raise error

     # Use provided group_id, if None, use public_groups config (can be list or single)
     # If public_groups is a list, send to all; if single, send to one
     target_group_ids = []
     if group_id is not None:
         target_group_ids = [str(group_id)] # Ensure it's a list of strings
     else:
         cfg_groups = NIUNIUBOT_CONFIG.get("public_groups")
         if isinstance(cfg_groups, list):
             target_group_ids = [str(g) for g in cfg_groups]
         elif isinstance(cfg_groups, (int, str)):
             target_group_ids = [str(cfg_groups)]
         else:
             logger.error("send_group_msg: group_id 参数为空且 public_groups 配置无效")
             return None

     if not target_group_ids:
          logger.error("send_group_msg: 没有指定群组ID或 public_groups 配置为空")
          return None

     token_to_use = access_token or NIUNIUBOT_CONFIG.get("token", "") # token 可选

     message_array = []
     # Longer timeout if sending image
     timeout_total = 30 if (image_file or image_url) else 10

     if reply_some:
         message_array.append({"type": "reply", "data": {"id": str(reply_some)}}) # Ensure reply ID is string

     # Add json_data first if provided (allows custom elements)
     if json_data:
         if isinstance(json_data, list): # Ensure json_data is a list of message segments
              for item in json_data:
                  # Avoid adding reply segment again if it's already handled by reply_some
                  if item.get("type") != "reply":
                      message_array.append(item)
         else:
              logger.warning("send_group_msg: json_data 不是列表格式，忽略。")


     if at_someone is not None:
         message_array.append({"type": "at", "data": {"qq": str(at_someone)}}) # Ensure at_someone is string

     # Add text segment after at if both exist, with a space
     text_content = text
     if text_content:
         # If text exists and the last segment was an @, add a leading space
         # This prevents "@user文本" and makes it "@user 文本"
         # However, the current code adds text AFTER at, so the space is already added in cmd_help
         # Let's keep the logic simple, add space here if needed.
         # Simplified: just add the text. If @ was added, the next text segment naturally follows.
         # The original code added f" {text}" if at_someone was present.
         # Let's replicate that logic or simplify. Adding text always as a separate segment is cleaner.
         message_array.append({"type": "text", "data": {"text": " "+text_content}})


     if image_file:
         abs_image_file = os.path.abspath(image_file)
         if not os.path.exists(abs_image_file):
             logger.error(f"send_group_msg: 图片文件不存在: {abs_image_file}")
             # Decide behavior: raise error or just skip image? Let's raise.
             raise FileNotFoundError(f"图片文件不存在: {abs_image_file}")
         message_array.append({"type": "image", "data": {"file": f"file://{abs_image_file}"}}) # Use file:// for local path

     if image_url:
         # As per go-cqhttp, use 'url' key for external URLs
         message_array.append({"type": "image", "data": {"url": image_url}})
         # If your CQHTTP flavor requires 'file' for URLs, change 'url' to 'file' here.
         #message_array.append({"type": "image", "data": {"file": image_url}}) # Alternative for some clients


     if not message_array:
         logger.warning("send_group_msg: 没有提供任何消息内容，跳过发送。")
         return None # No content to send

     # Send message to all target groups
     message_ids = []
     for target_group_id in target_group_ids:
         payload = {"group_id": target_group_id, "message": message_array}
         params_dict = {"access_token": token_to_use} if token_to_use else {}
         try:
             async with aiohttp.ClientSession() as session:
                 # Use ClientTimeout object
                 timeout = aiohttp.ClientTimeout(total=timeout_total)
                 async with session.post(f"{base_url}/send_group_msg", json=payload, params=params_dict, timeout=timeout) as response:
                     response.raise_for_status()
                     data = await response.json()
                     if data.get("status") == "ok":
                         sent_message_id = data["data"].get("message_id")
                         if sent_message_id:
                             message_ids.append(sent_message_id)
                         else:
                             logger.warning(f"send_group_msg: 发送到群组 {target_group_id} 成功但未返回 message_id。")
                     else:
                         logger.error(f"send_group_msg: 发送到群组 {target_group_id} 失败: {data.get('message')}, 错误码: {data.get('retcode')}")
         except aiohttp.ClientError as e:
             logger.error(f"send_group_msg: 发送群消息到 {target_group_id} 失败: {str(e)}", exc_info=True)
         except Exception as e:
              logger.error(f"send_group_msg: 发送群消息到 {target_group_id} 发生未知错误: {str(e)}", exc_info=True)


     # If multiple messages sent, return the list of IDs. If one, return that ID.
     # If none sent successfully, return None.
     if len(message_ids) == 1:
         return message_ids[0]
     elif len(message_ids) > 1:
         return message_ids # Return list
     else:
         return None # Failed to send to any group


# 异步 send_private_msg 函数
async def send_private_msg(user_id, text, access_token=None):
     
     base_url = NIUNIUBOT_CONFIG.get("address", "")
     if not base_url:
         logger.error("send_private_msg: base_url 配置缺失，请检查config.json")
         return None

     token_to_use = access_token or NIUNIUBOT_CONFIG.get("token", "") # token 可选

     if user_id is None or not text:
         logger.error("send_private_msg: user_id 或 text 参数缺失")
         return None

     # Message should be a message array
     message_array = [{"type": "text", "data": {"text": text}}]

     payload = {"user_id": str(user_id), "message": message_array} # Ensure user_id is string
     params_dict = {"access_token": token_to_use} if token_to_use else {}

     async with aiohttp.ClientSession() as session:
         try:
             timeout = aiohttp.ClientTimeout(total=10)
             async with session.post(f"{base_url}/send_private_msg", json=payload, params=params_dict, timeout=timeout) as response:
                 response.raise_for_status()
                 data = await response.json()
                 if data.get("status") == "ok":
                     # Private message usually returns message_id in data, not data['data']
                     return data.get("data", {}).get("message_id")
                 else:
                     logger.error(f"send_private_msg API 错误: {data.get('message')}, 错误码: {data.get('retcode')}")
                     return None
         except aiohttp.ClientError as e:
             logger.error(f"send_private_msg 网络请求失败: {str(e)}", exc_info=True)
             return None
         except Exception as e:
             logger.error(f"send_private_msg 错误: {str(e)}", exc_info=True)
             return None


# 命令处理函数映射
command_handlers = {}

# 注册命令处理函数
def register_command(command_name):
     def decorator(func):
         command_handlers[command_name] = func
         return func
     return decorator

# 修改后的 handle_message 函数：使用 message 数组解析命令
async def handle_message(message_data):
     # This function now runs within the global_asyncio_loop thread
     try:
         message_type = message_data.get("post_type") # Changed from message_type to post_type based on log
         if message_type != "message": # Only process message events
             return

         # Get group_id or user_id based on actual message_type
         group_id = message_data.get("group_id")
         user_id = message_data.get("user_id") # Sender's user_id
         is_private = (message_data.get("message_type") == "private") # Check message_type field for private/group

         # Check if message is from allowed groups
         if not is_private and group_id not in NIUNIUBOT_CONFIG.get("active_groups", []):
             return

         # New: Extract sender role and calculate is_admin based on message_data structure
         sender = message_data.get("sender", {})
         # For group messages, check role. For private, check if sender is admin_qq
         if is_private:
              admin_qq = str(NIUNIUBOT_CONFIG.get("admin_qq", "")) # Get admin_qq from config
              # If admin_qq is configured, private messages from admin_qq are treated as admin
              is_admin = (str(user_id) == admin_qq and admin_qq != "")
         else: # Group message
              role = sender.get("role", "member") # Default role is 'member'
              is_admin = (role == "owner" or role == "admin") # Owner or admin have admin privileges

         # Parse message array, extract all text content
         message_array = message_data.get("message", [])
         raw_text = ""
         for item in message_array:
             if item.get("type") == "text":
                 raw_text += item["data"].get("text", "") # Extract text part, keep internal spaces for now

         # Remove potential leading/trailing whitespace from the whole text block
         raw_text = raw_text.strip()

         # Check if the text starts with a command prefix (e.g., '/', '!', or nothing depending on setup)
         # Assuming commands are like "command args" at the start of the message
         if raw_text:
             command_parts = raw_text.split(maxsplit=1)
             if command_parts:
                 command = command_parts[0].lower()
                 args = command_parts[1] if len(command_parts) > 1 else ""

                 if command in command_handlers:
                      logger.info(f"接收到命令: {command}, 参数: {args}, from user: {user_id}, group: {group_id}")
                      # Pass necessary info to the command handler
                      await command_handlers[command](group_id, user_id, args, is_private=is_private, is_admin=is_admin,
                                                      message_data=message_data)
     except Exception as e:
         logger.error(f"处理消息失败: {str(e)}", exc_info=True)


# 注册命令处理函数
# ... (所有 @register_command("...") async def cmd_... 函数保持不变) ...
# nhelp
@register_command("nhelp")
async def cmd_help(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
     help_text = get_help_text(NIUNIUBOT_CONFIG)
     try:
         if is_private:
             await send_private_msg(user_id, help_text)
         else:
             # Ensure at_someone is used correctly with send_group_msg
             await send_group_msg(group_id=group_id, text=help_text, at_someone=user_id)
     except Exception as e:
         logger.error(f"cmd_help 执行失败: {str(e)}", exc_info=True)
         if not is_private: # Only respond in group if it failed in group
             await send_group_msg(group_id=group_id, text="获取帮助信息失败。", at_someone=user_id)


# 2 (服务器列表)
@register_command("2")
async def cmd_server(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
     try:
         # Assuming 3889013937 is the QQ ID of the bot that provides server list
         # Ensure server_group_name is correctly accessed
         current_server_group_name = NIUNIUBOT_CONFIG.get("server_group_name", "默认群组名")
         await send_group_msg(group_id=group_id, text=f"/server {current_server_group_name}", at_someone=3889013937)
     except Exception as e:
         logger.error(f"cmd_server 执行失败: {str(e)}", exc_info=True)
         if not is_private:
             await send_group_msg(group_id=group_id, text="查询服务器列表失败。", at_someone=user_id)


# get_gameid_by_name (辅助函数)
async def get_gameid_by_name(server_name):
    
    if server_name is None or server_name.strip() == "":
        return {"success": False, "result": "找不到服务器，请纠正查询参数"} # Return dict directly

    url = f"https://api.gametools.network/bfv/servers/?name={server_name}"
    max_retries = 3
    timeout = 10 # Increased timeout
    retry_delay = 2 # Increased retry delay

    for attempt in range(1, max_retries + 1):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=timeout) as response:
                    if response.status == 200:
                        try:
                            data = await response.json()
                            servers = data.get('servers', [])
                            if len(servers) == 1:
                                game_id = servers[0].get('gameId', None)
                                if game_id is not None:
                                    return {"success": True, "result": str(game_id)} # Ensure game_id is string
                                else:
                                    return {"success": False, "result": "在返回数据中找不到gameid"}
                            else:
                                # Return names of multiple servers found for better user feedback
                                found_names = ", ".join([s.get('serverName', '未知') for s in servers if isinstance(s, dict)])
                                return {"success": False, "result": f"找到多个匹配的服务器: {found_names}，请纠正查询参数"}

                        except json.JSONDecodeError as e:
                            error_msg = f"JSON decode error: {str(e)}"
                            logger.error(f"get_gameid_by_name 尝试 {attempt}: {error_msg}", exc_info=True)
                            if attempt < max_retries:
                                logger.info(f"get_gameid_by_name 等待 {retry_delay} 秒后重试...")
                                await asyncio.sleep(retry_delay)
                            continue # Continue to next attempt

                    else:
                        # HTTP status code not 200
                        error_msg = f"HTTP error with status code: {response.status}"
                        logger.error(f"get_gameid_by_name 尝试 {attempt}: {error_msg}")
                        if attempt < max_retries:
                            logger.info(f"get_gameid_by_name 等待 {retry_delay} 秒后重试...")
                            await asyncio.sleep(retry_delay)
                        continue # Continue to next attempt

        except aiohttp.ClientError as e:
            error_msg = f"Request exception: {str(e)}"
            logger.error(f"get_gameid_by_name 尝试 {attempt}: {error_msg}", exc_info=True)
            if attempt < max_retries:
                logger.info(f"get_gameid_by_name 等待 {retry_delay} 秒后重试...")
                await asyncio.sleep(retry_delay)
            continue # Continue to next attempt

    logger.error("get_gameid_by_name 所有尝试都失败了。")
    return {"success": False, "result": "所有尝试都失败了，请重试或联系管理员"} # Return dict on final failure


# /stop
@register_command("/stop")
async def cmd_stop_nfj(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
     
     try:
         config = NIUNIUBOT_CONFIG # Access config
         if not config.get("nfj_stop", False): # Check boolean directly
             logger.info("/stop 命令已禁用。")
             if not is_private:
                  await send_group_msg(group_id, at_someone=user_id, text="stop 命令已禁用。")
             return

         # Check if the command is from the allowed group if configured
         nfj_group_id = config.get("nfj_group")
         if nfj_group_id is not None and str(group_id) != str(nfj_group_id) and not is_private:
              logger.warning(f"/stop 命令在不允许的群组 {group_id} 执行。")
              if not is_private:
                   await send_group_msg(group_id, at_someone=user_id, text="此命令只能在特定群组或私聊中使用。")
              return

         # Check if args is digits and length 14
         args_stripped = args.strip()
         if args_stripped.isdigit() and len(args_stripped) == 14:
             game_id = args_stripped
             logger.info(f"接收到 /stop 命令带 game_id: {game_id}")
             # Assuming 3889013937 is the target bot ID
             await send_group_msg(group_id=group_id, text=f"/stop {game_id}", at_someone=3889013937)
         else:
             # Call async get_gameid_by_name and get dictionary result
             logger.info(f"接收到 /stop 命令带服务器名: {args_stripped}")
             result_data = await get_gameid_by_name(args_stripped)

             if result_data.get("success"):
                 game_id = result_data.get("result")
                 logger.info(f"找到 game_id: {game_id}，发送 /stop 命令。")
                 # Assuming 3889013937 is the target bot ID
                 await send_group_msg(group_id=group_id, text=f"/stop {game_id}", at_someone=3889013937)
             else:
                 error_msg = result_data.get("result", "未知错误")
                 logger.warning(f"/stop 命令查找 game_id 失败: {error_msg}")
                 if not is_private:
                      await send_group_msg(group_id, at_someone=user_id, text=f"stop命令执行失败: {error_msg}")
                 else:
                      await send_private_msg(user_id, text=f"stop命令执行失败: {error_msg}")

     except Exception as e:
         logger.error(f"/stop 命令执行失败: {str(e)}", exc_info=True)
         error_response = "stop 命令执行失败，请联系管理员"
         if not is_private:
             await send_group_msg(group_id, at_someone=user_id, text=error_response)
         else:
             await send_private_msg(user_id, text=error_response)

async def get_group_member_list(base_url=None, group_id=None, no_cache="true"):
    """
    异步调用 API 获取指定群组的成员列表，支持单个或多个 group_id，并合并结果为一个列表。

    参数:
    - base_url: API 的基础 URL，默认为 None。如果未提供，将使用 "http://127.0.0.1:3000"。
    - group_id: 群组 ID，可以是单个字符串/数字，或一个列表/数组。默认为 None，如果未提供，将打印错误并返回空列表。
    - no_cache: 是否不使用缓存，默认为 "true"（字符串）。

    返回:
    - 成员列表（数组），合并了所有指定群组的成员。如果 API 调用失败，返回空列表或部分结果。

    示例调用:
    - 单个 group_id: members = await get_group_member_list(group_id="978880814")
    - 多个 group_id: members = await get_group_member_list(group_id=["978880814", "976420087"])
    """
    if base_url is None:
        base_url = "http://127.0.0.1:3000"  # 默认 API 地址
    if group_id is None:
        print("请提供群号")
        return []

    # 检查 group_id 类型，如果是单个值，转换为列表以统一处理
    if not isinstance(group_id, (list, tuple)):
        group_id_list = [group_id]  # 转换为列表
    else:
        group_id_list = group_id  # 已经是列表

    all_members = []  # 用于存储所有成员列表

    async with aiohttp.ClientSession() as session:
        tasks = []
        for gid in group_id_list:
            # 直接在循环中定义并添加异步任务
            async def fetch_task(gid=gid):  # 使用默认参数避免闭包问题
                api_url = f"{base_url}/get_group_member_list"
                payload = {
                    "group_id": gid,
                    "no_cache": no_cache
                }
                async with session.post(api_url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("status") == "ok":
                            return data.get("data", [])  # 返回成员列表
                        else:
                            print(f"API 返回错误 for group_id {gid}: {data.get('message', '未知错误')}")
                            return []
                    else:
                        print(f"请求失败 for group_id {gid}，状态码: {response.status}")
                        return []

            tasks.append(fetch_task())  # 添加任务

        results = await asyncio.gather(*tasks, return_exceptions=True)  # 并发执行任务，捕获异常

        for result in results:
            if isinstance(result, Exception):
                print(f"API 调用异常: {str(result)}")
            elif result is not None:
                all_members.extend(result)  # 合并成员列表

    return all_members


async def check_card_in_members(card, members):
    """
    异步检查成员列表中是否存在指定的群昵称（card），使用不区分大小写的子字符串匹配。

    参数:
    - card: 要检查的群昵称（字符串），例如 "ccyniuniu"。会进行子字符串匹配（例如 "管理Ccyniuniu" 能被匹配）。
    - members: 成员列表（数组），由 get_group_member_list 函数返回。

    返回:
    - True: 如果昵称（或其子字符串）存在于成员列表中（不区分大小写）。
    - False: 如果昵称不存在。

    示例输出:
    - 如果匹配到，会打印 "{card} 在群里" 并返回 True。
    - 如果未匹配到，会打印 "{card} 不在群里" 并返回 False。
    """
    for member in members:
        member_card = member.get("card", "").lower()  # 将成员的 card 转为小写
        search_card = card.lower()  # 将输入的 card 转为小写
        if search_card in member_card:  # 子字符串匹配
            print(f"{card} 在群里")  # 输出匹配信息
            return True
    # 遍历完未找到匹配
    print(f"{card} 不在群里")  # 输出未匹配信息
    return False

@register_command("bc")
async def cmd_bc(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
    try:
        if not is_admin:
            await send_group_msg(group_id, at_someone=user_id, text="警告: 你没有权限，只有群主或管理员可以执行该命令。")
            return
        args_parts = args.strip().split(maxsplit=1)
        if len(args_parts) < 1:
            await send_group_msg(group_id, at_someone=user_id,
                                 text="命令格式错误，请提供用户名和踢出原因，例如: bc username")
            return
        username = args_parts[0].strip()
        # 获取所有活跃群组的 ID 列表
        active_groups = NIUNIUBOT_CONFIG.get("active_groups", [])
        if not active_groups:
            await send_group_msg(group_id, text="群号读取异常，请联系管理员")
            return

        # 获取所有活跃群组的成员列表（合并）
        members = await get_group_member_list(group_id=active_groups)
        if not members:
            await send_group_msg(group_id, text="群成员信息读取异常，请联系管理员")
            return
        check = await check_card_in_members(card=username,members=members)
        current_server_group_name = NIUNIUBOT_CONFIG.get("server_group_name", "2636")
        if not check:
            await send_group_msg(group_id=group_id, text=f"/tb {current_server_group_name} {username} 超杀未进群",
                                 at_someone=3889013937)
            return True
        else:
            await send_group_msg(group_id=group_id, text=f"{username} 已进群",
                                 at_someone=user_id)

    except Exception as e:
        logger.error(f"cmd_bc 执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id, text="bc命令执行失败")

# kick, ban, tb, ub, lb commands
# ... (These commands remain largely the same, ensure they use await send_group_msg/send_private_msg) ...
# Make sure they correctly check is_admin and access NIUNIUBOT_CONFIG

@register_command("kick")
async def cmd_kick(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
    
    try:
        if not is_admin:
            await send_group_msg(group_id, at_someone=user_id, text="警告: 你没有权限，只有群主或管理员可以执行该命令。")
            return
        args_parts = args.strip().split(maxsplit=1)
        if len(args_parts) < 2:
            await send_group_msg(group_id, at_someone=user_id, text="命令格式错误，请提供用户名和踢出原因，例如: kick username reason")
            return
        username = args_parts[0].strip()
        reason = args_parts[1].strip()
        # Assuming 3889013937 is the target bot ID
        await send_group_msg(group_id=group_id, text=f"/kick {username} {reason}", at_someone=3889013937)
    except Exception as e:
        logger.error(f"cmd_kick 执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id, text="kick命令执行失败")

@register_command("ban")
async def cmd_ban(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
    
    try:
        if not is_admin:
            await send_group_msg(group_id, at_someone=user_id, text="警告: 你没有权限，只有群主或管理员可以执行该命令。")
            return
        args_parts = args.strip().split(maxsplit=1)
        if len(args_parts) < 2:
            await send_group_msg(group_id, at_someone=user_id, text="命令格式错误，请提供用户名和ban原因，例如: ban username reason")
            return
        username = args_parts[0].strip()
        reason = args_parts[1].strip()
        current_server_group_name = NIUNIUBOT_CONFIG.get("server_group_name", "默认群组名")
        # Assuming 3889013937 is the target bot ID
        await send_group_msg(group_id=group_id, text=f"/ban {current_server_group_name} {username} {reason}", at_someone=3889013937)
    except Exception as e:
        logger.error(f"cmd_ban 执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id, text="ban命令执行失败")

@register_command("tb")
async def cmd_tb(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
    
    try:
        if not is_admin:
            await send_group_msg(group_id, at_someone=user_id, text="警告: 你没有权限，只有群主或管理员可以执行该命令。")
            return
        args_parts = args.strip().split(maxsplit=1)
        if len(args_parts) < 2:
            await send_group_msg(group_id, at_someone=user_id, text="命令格式错误，请提供用户名和ban原因，例如: tb username reason")
            return
        username = args_parts[0].strip()
        reason = args_parts[1].strip()
        current_server_group_name = NIUNIUBOT_CONFIG.get("server_group_name", "默认群组名")
        # Assuming 3889013937 is the target bot ID
        await send_group_msg(group_id=group_id, text=f"/tb {current_server_group_name} {username} {reason}", at_someone=3889013937)
    except Exception as e:
        logger.error(f"cmd_tb 执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id, text="tb命令执行失败")

@register_command("ub")
async def cmd_ub(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
    
    try:
        if not is_admin:
            await send_group_msg(group_id, at_someone=user_id, text="警告: 你没有权限，只有群主或管理员可以执行该命令。")
            return
        if not args:
            await send_group_msg(group_id, at_someone=user_id, text="命令格式错误，请提供用户名，例如: ub username")
            return
        username = args.strip()
        current_server_group_name = NIUNIUBOT_CONFIG.get("server_group_name", "默认群组名")
        # Assuming 3889013937 is the target bot ID
        await send_group_msg(group_id=group_id, text=f"/unban {current_server_group_name} {username}", at_someone=3889013937)
    except Exception as e:
        logger.error(f"cmd_ub 执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id, text="ub命令执行失败")

@register_command("lb")
async def cmd_lb(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
    
    try:
        card = "" # Initialize card
        try:
            # Use the global loop for waiting
            bot_data = await asyncio.wait_for(get_group_member_info(group_id=group_id, user_id=user_id), timeout=5)
            card = bot_data.get('card', '').strip() # Get card and strip whitespace
        except asyncio.TimeoutError:
            logger.error("cmd_lb: get_group_member_info 调用超时，昵称设置为默认空值")
            card = ""
        except Exception as e:
            logger.error(f"cmd_lb: 获取用户昵称失败: {str(e)}，昵称设置为默认空值", exc_info=True)
            card = ""

        username = args.strip()
        if not username:
            if not card:
                await send_group_msg(group_id, at_someone=user_id, text="未提供查询的玩家id，也无法获取你的群内昵称，命令停止执行")
                return
            await send_group_msg(group_id, at_someone=user_id, text="未提供查询的玩家id，将使用你的群内昵称作为id查询")
            username_to_use = card
        else:
            username_to_use = username

        # Assuming 3889013937 is the target bot ID
        await send_group_msg(group_id=group_id, text=f"/listban {username_to_use}", at_someone=3889013937)

    except Exception as e:
        logger.error(f"cmd_lb 执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id, text="lb命令执行失败")


is_nfj_using = False
@register_command("nfj")
async def cmd_nfj(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
    
    try:
        global is_nfj_using
        if not args:
            await send_group_msg(group_id, at_someone=user_id, text="请提供要叫暖服机的服务器序号")
            return

        config = NIUNIUBOT_CONFIG # Access config
        args_lower = args.lower().strip()

        nfj_server_config = config.get("nfj_server", {})
        if not isinstance(nfj_server_config, dict):
            logger.error("nfj_server 配置无效或缺失，请检查config.json")
            await send_group_msg(group_id, at_someone=user_id, text="系统错误: 暖服机服务器配置无效，请联系管理员。")
            return

        if args_lower not in nfj_server_config:
            available_servers = ', '.join(nfj_server_config.keys())
            error_message = f"请提供正确的服务器序号，当前可呼叫暖服机的服务器有：{available_servers}"
            await send_group_msg(group_id, at_someone=user_id, text=error_message)
            return

        if is_nfj_using:
            await send_group_msg(group_id, at_someone=user_id, text="暖服机指令正在转发，请稍等一会再叫暖服机")
            return

        is_nfj_using = True

        nfj_group_id = config.get("nfj_group")
        nfj_bot = config.get("nfj_bot") # nfj_bot seems unused in this command

        if not nfj_group_id: # Only check nfj_group_id as it's used for sending
             logger.error("nfj_group 配置缺失或无效，请检查config.json")
             await send_group_msg(group_id, at_someone=user_id, text="系统错误: 暖服机群组配置无效，请联系管理员。")
             return

        if not config.get("nfj_on", False): # Check boolean directly
            await send_group_msg(group_id, at_someone=user_id, text="暖服机指令未启用，正在呼叫管理员为你叫暖服机")
            admin_qq = config.get("admin_qq") # Assuming admin_qq is the contact for manual call
            if admin_qq:
                 # Send reminder to admin via private message or group message
                 await send_private_msg(user_id=admin_qq, text=f"有群成员需要暖服机: 服务器序号 {args_lower}, 来自群组 {group_id}, 用户 {user_id}")
                 await send_group_msg(group_id=group_id, text="已通知管理员。", at_someone=user_id) # Confirm to user
            else:
                 await send_group_msg(group_id, at_someone=user_id, text="未配置管理员QQ，无法通知管理员。请联系群管理员。")
            return # Stop here if nfj is off


        current_server_group_name = config.get("server_group_name", "默认群组名")
        send_msg_id = await send_group_msg(group_id=nfj_group_id, text=f"/warm {current_server_group_name} {args_lower}")

        if send_msg_id is None:
             logger.error(f"发送 /warm 命令到群组 {nfj_group_id} 失败。")
             await send_group_msg(group_id, at_someone=user_id, text="发送暖服机命令失败，请稍后重试。")
             return

        # Use the correct group_id (where the response is expected) which is nfj_group_id
        def nfj_match(msg):
            raw_message_lower = msg.get('raw_message', '').lower()
            # Match if server_group_name and args are in message, OR '队列' is in message
            # Also consider the response might come from the nfj_bot itself (mentioning it)
            bot_qq = config.get("nfj_bot") # Get nfj_bot QQ
            bot_mention = f'[cq:at,qq={bot_qq}]' if bot_qq else ''
            return (current_server_group_name.lower() in raw_message_lower and args_lower in raw_message_lower) or \
                   ('队列' in raw_message_lower) or \
                   (bot_mention and bot_mention in raw_message_lower) # Match @nfj_bot

        rsp_data = await check_rsp(msg_id=send_msg_id, group_id=nfj_group_id, match_condition=nfj_match)

        # If check_rsp fails or times out, it returns an error message array
        # Send the response data back to the original group
        await send_group_msg(group_id, at_someone=user_id, json_data=rsp_data)
        logger.info(f"已成功转发 /warm {current_server_group_name} {args_lower} 命令结果到群组 {group_id}")

    except Exception as e:
        logger.error(f"cmd_nfj 命令执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id, text="命令执行失败，请联系管理员。")
    finally:
        is_nfj_using = False # Reset the flag


is_nfb_using = False
@register_command("nfb")
async def cmd_nfb(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
    
    try:
        global is_nfb_using
        if is_nfb_using:
            await send_group_msg(group_id, at_someone=user_id, text="暖服机指令正在转发，请稍等一会再查询暖服机状态")
            return

        is_nfb_using = True
        config = NIUNIUBOT_CONFIG

        nfj_group_id = config.get("nfj_group")
        # nfj_bot = config.get("nfj_bot") # nfj_bot seems unused

        if not nfj_group_id:
            logger.error("nfj_group 配置缺失或无效，请检查config.json")
            await send_group_msg(group_id, at_someone=user_id, text="系统错误: 暖服机群组配置无效，请联系管理员。")
            return

        if not config.get("nfj_on", False):
            await send_group_msg(group_id, at_someone=user_id, text="暖服机查询指令未启用")
            return

        # Send /nfb command to the specific group
        send_msg_id = await send_group_msg(group_id=nfj_group_id, text=f"/nfb")

        if send_msg_id is None:
             logger.error(f"发送 /nfb 命令到群组 {nfj_group_id} 失败。")
             await send_group_msg(group_id, at_someone=user_id, text="发送暖服机查询命令失败，请稍后重试。")
             return

        # Use the correct group_id for checking response
        def nfb_match(msg):
            raw_message_lower = msg.get('raw_message', '').lower()
            # Match if any of the keywords is in the message
            # Also consider the response might come from the nfj_bot itself (mentioning it)
            bot_qq = config.get("nfj_bot") # Get nfj_bot QQ
            bot_mention = f'[cq:at,qq={bot_qq}]' if bot_qq else ''
            return '暖服中' in raw_message_lower or \
                   '等待' in raw_message_lower or \
                   '无在暖服务器' in raw_message_lower or \
                   (bot_mention and bot_mention in raw_message_lower) # Match @nfj_bot

        rsp_data = await check_rsp(msg_id=send_msg_id, group_id=nfj_group_id, match_condition=nfb_match)

        # Send the response data back to the original group
        await send_group_msg(group_id, at_someone=user_id, json_data=rsp_data)
        logger.info(f"已成功转发 /nfb 命令结果到群组 {group_id}")

    except Exception as e:
        logger.error(f"cmd_nfb 命令执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id, text="命令执行失败，请联系管理员。")
    finally:
        is_nfb_using = False # Reset the flag


# 新增辅助函数：从消息数组中提取图片URL
def extract_image_url_from_message(message_array):
    
    if not isinstance(message_array, list):
        return None
    for item in message_array:
        if item.get("type") == "image":
            # Check for both 'url' (recommended for remote) and 'file' (used in some contexts)
            url = item.get("data", {}).get("url")
            if url:
                return url
            # Fallback for 'file' if it contains a URL string
            file_path_or_url = item.get("data", {}).get("file")
            if file_path_or_url and (file_path_or_url.startswith('http://') or file_path_or_url.startswith('https://')):
                 return file_path_or_url
    return None


# 处理嘴臭举报命令 zc
@register_command("zc")
async def cmd_zc(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
     
     try:
         if message_data is None:
             await send_group_msg(group_id, at_someone=user_id, text="消息数据缺失，无法处理举报")
             return

         config = NIUNIUBOT_CONFIG # Access config

         args_parts = args.strip().split()
         if len(args_parts) < 1:
             await send_group_msg(group_id, at_someone=user_id, text="请提供服务器号（例如 zc s1），并确保消息包含图片或回复图片消息")
             return

         server_key = args_parts[0].lower()

         our_server_config = config.get("our_server", {})
         if not isinstance(our_server_config, dict) or server_key not in our_server_config:
             available_servers = ', '.join(our_server_config.keys()) if our_server_config else "无"
             await send_group_msg(group_id, at_someone=user_id, text=f"无效的服务器号。当前可用服务器: {available_servers}")
             return

         # 发送处理确认消息
         # Use the user_id from message_data as it's the actual sender
         await send_group_msg(group_id, at_someone=user_id, text=f"已接收到举报，正在处理服务器 {server_key} 的玩家数据...")

         game_id = our_server_config[server_key]

         # Get players data asynchronously
         players_data = await async_get_players_gametools(game_id=game_id)

         player_list_str = None
         if players_data and 'teams' in players_data:
             player_names = []
             for team in players_data.get("teams", []):
                 for player in team.get("players", []):
                     player_name = player.get("name", "").strip() # Keep original casing for AI
                     if player_name: # Only add non-empty names
                          player_names.append(player_name)
             player_list_str = ", ".join(player_names)
         else:
              # It's okay if players_data is not available, just log it and proceed without player list
              logger.warning(f"cmd_zc: 无法获取服务器 {server_key} 的玩家数据，AI判断将不含玩家列表。")
              # Optionally send a message to the user about this
              # await send_group_msg(group_id, text=f"注意: 无法获取服务器 {server_key} 的当前玩家列表。")
              player_list_str = "" # Ensure it's an empty string if data fetch fails


         message_array = message_data.get("message", [])
         reply_id = None
         image_url = None

         for item in message_array:
             if item.get("type") == "reply":
                 reply_id = item["data"].get("id")
             elif item.get("type") == "image":
                 # Directly get image URL from the current message if present
                 url = item.get("data", {}).get("url")
                 if url:
                     image_url = url # Use URL from the current message

         if reply_id:
             # If there's a reply, prioritize getting image from the replied message
             logger.info(f"cmd_zc: 检测到回复消息，目标消息ID: {reply_id}")
             try:
                 # Use the global loop for waiting
                 message_detail = await asyncio.wait_for(get_message_detail(message_id=reply_id, config=config), timeout=15) # Add timeout
                 detail_message_array = message_detail.get("message", [])
                 # Extract image URL from the replied message's content
                 image_url_from_reply = extract_image_url_from_message(detail_message_array)
                 if image_url_from_reply:
                      image_url = image_url_from_reply # Use this URL if found
                 else:
                     # If reply had no image, and current message didn't either
                     if not image_url:
                         await send_group_msg(group_id, at_someone=user_id, text="被回复的消息中未找到图片，当前消息中也没有图片，无法完成举报")
                         return
             except asyncio.TimeoutError:
                 logger.error(f"cmd_zc: 获取被回复消息详情超时 (msg_id {reply_id})")
                 # If fetching reply fails, check if current message has an image
                 if not image_url:
                     await send_group_msg(group_id, at_someone=user_id, text="获取被回复的消息详情超时，且当前消息无图片，无法完成举报")
                     return
                 else:
                     logger.warning("cmd_zc: 获取被回复消息详情超时，但当前消息包含图片，将尝试使用当前图片进行AI判断。")

             except Exception as e:
                 logger.error(f"cmd_zc: 获取被回复消息详情失败 (msg_id {reply_id}): {str(e)}", exc_info=True)
                  # If fetching reply fails, check if current message has an image
                 if not image_url:
                     await send_group_msg(group_id, at_someone=user_id, text=f"获取被回复的消息详情失败，且当前消息无图片，无法完成举报。错误: {e}")
                     return
                 else:
                      logger.warning("cmd_zc: 获取被回复消息详情失败，但当前消息包含图片，将尝试使用当前图片进行AI判断。")


         # Final check if we have an image URL after considering reply and current message
         if not image_url:
             await send_group_msg(group_id, at_someone=user_id, text="请提供证据（单张图片），或回复包含图片的消息")
             return

         # Call AI judgment asynchronously
         logger.info(f"cmd_zc: 正在调用AI处理图片...")
         # Pass the user_id from message_data as from_who
         judgment_result = await ai_judgment(image_url=image_url, from_who=user_id, player_list_str=player_list_str)
         logger.info(f"cmd_zc: AI处理结束，结果类型: {type(judgment_result)}")

         if isinstance(judgment_result, dict):
             # Successfully got a dictionary result from AI
             judgment = judgment_result.get("judgment")
             player = judgment_result.get("player", "未提取到玩家")
             chat = judgment_result.get("chat", "未提取到对话内容")

             # Log the raw AI result for debugging
             logger.debug(f"cmd_zc: AI原始结果: {judgment_result}")

             if judgment == "1": # Assuming "1" means detected language attack
                 logger.info(f"cmd_zc: AI检测到语言攻击。玩家: {player}, 对话: {chat}")
                 # Ensure player string is not just the default '未提取到玩家' if AI couldn't find it
                 if player == "未提取到玩家" or not player.strip():
                      await send_group_msg(group_id, at_someone=user_id, text=f"AI检测到可能有语言攻击，但未能提取到玩家信息，请联系管理员检查。对话内容:\n{chat}")
                      return # Stop if player info is missing

                 # Split player string by commas and strip spaces
                 players_to_ban = [p.strip() for p in player.split(',') if p.strip()]

                 if not players_to_ban:
                      await send_group_msg(group_id, at_someone=user_id, text=f"AI检测到可能有语言攻击，但未能解析到有效的玩家名字，请联系管理员检查。对话内容:\n{chat}")
                      return # Stop if player names are invalid after split

                 # Construct ban message for admin
                 ban_text = "\n".join([f"ban {p} 语言攻击" for p in players_to_ban])

                 admin_qq = config.get("check_admin") # Assuming check_admin is admin's QQ ID
                 if not admin_qq:
                     logger.error("cmd_zc: check_admin 配置缺失，请检查config.json")
                     await send_group_msg(group_id, at_someone=user_id, text="AI检测到语言攻击，但未配置管理员QQ，无法通知管理员进行处理。请联系群管理员。")
                     return

                 await send_group_msg(group_id, at_someone=user_id, text=f"\n因AI仍存在误差,为不引发争议,正在请管理员判定\n对话内容:\n{chat}\nAI检测到玩家: {player} 有语言攻击。")
                 # Send ban command suggestions to the admin QQ
                 await send_group_msg(group_id, at_someone="2720216977", text=f"接到用户 {user_id} (群组 {group_id}) 举报的语言攻击，服务器 {server_key}。AI检测到玩家 {player}，请在确认后发送下面的命令:\n{ban_text}")
                 logger.info(f"cmd_zc: 已通知管理员 {admin_qq} 关于语言攻击举报。")


             else: # Assuming anything other than "1" means no language attack detected or failed detection
                 logger.info(f"cmd_zc: AI未检测到语言攻击。玩家: {player}, 对话: {chat}")
                 await send_group_msg(group_id, at_someone=user_id, text=f"\n对话内容:\n{chat}\n玩家: {player}\n未检测到有语言攻击，如有异议请联系管理员")

         elif isinstance(judgment_result, str) and "失败" in judgment_result:
             # AI judgment returned a failure string
             logger.error(f"cmd_zc: AI判断返回失败字符串: {judgment_result}")
             await send_group_msg(group_id, at_someone=user_id, text=f"AI处理失败: {judgment_result}. 请重试或联系管理员。")

         else:
             # Unexpected result type from AI
             logger.error(f"cmd_zc: AI判断返回异常结果: {judgment_result}")
             await send_group_msg(group_id, at_someone=user_id, text="AI处理返回异常结果，请联系管理员检查。")

     except Exception as e:
         logger.error(f"cmd_zc 命令执行失败: {str(e)}", exc_info=True)
         await send_group_msg(group_id, at_someone=user_id, text="命令执行失败，请联系管理员")


def load_bot_names(file_path="nfj.txt"):
     
     try:
         with file_lock: # Use file lock
             if os.path.exists(file_path):
                 with open(file_path, 'r', encoding='utf-8') as f:
                     names = f.read().splitlines()
                     return set(name.strip().lower() for name in names)
             else:
                 logger.warning(f"机器人名单文件 {file_path} 不存在，使用空名单")
                 return set()
     except Exception as e:
         logger.error(f"读取机器人名单文件失败: {str(e)}", exc_info=True)
         return set()


# 新增全局变量：用于跟踪监控任务 (Keep this)
monitoring_tasks = {}

# 新增函数：提取自 cmd_snf 的暖服状态检查逻辑，便于重用 (Keep this)
async def check_nuanfu_status(server_key):
    
    try:
        # Use file lock for reading bot names file
        bot_names_set = load_bot_names() # Use the function with lock

        # 获取服务器配置和 game_id
        config = NIUNIUBOT_CONFIG # Access config
        our_server_config = config.get("our_server", {})
        if not isinstance(our_server_config, dict) or server_key not in our_server_config:
            available_servers = ', '.join(our_server_config.keys()) if our_server_config else "无"
            return {'success': False, 'error_msg': f"无效的服务器号。当前可用服务器: {available_servers}"}
        game_id = our_server_config[server_key]

        players_data = await async_get_players_gametools(game_id=game_id)

        if players_data and 'teams' in players_data:
            teams = players_data['teams']
            if len(teams) != 2:
                # Log this unexpected state
                logger.warning(f"check_nuanfu_status for {server_key}: 玩家数据团队数量异常 ({len(teams)} != 2)")
                # Continue processing if possible, or return error? Let's return error for now
                return {'success': False, 'error_msg': "玩家数据不完整（团队数量异常）。"}

            attacker_team_data = None
            defender_team_data = None
            for team in teams:
                if team.get('key') == 'USA_Pacific':
                    attacker_team_data = team
                elif team.get('key') == 'JPN':
                    defender_team_data = team

            if not attacker_team_data or not defender_team_data:
                logger.warning(f"check_nuanfu_status for {server_key}: 无法找到有效的团队数据（USA_Pacific 或 JPN）。")
                return {'success': False, 'error_msg': "无法找到有效的团队数据。"}

            def count_real_and_bot(team_data, bot_names_set):
                players = team_data.get('players', [])
                real_count = 0
                bot_count = 0
                for player in players:
                    name = player.get('name', '').strip().lower()
                    if name and name in bot_names_set: # Ensure name is not empty
                        bot_count += 1
                    elif name: # Ensure name is not empty before counting as real
                        real_count += 1
                return real_count, bot_count

            defender_real, defender_bot = count_real_and_bot(defender_team_data, bot_names_set)
            attacker_real, attacker_bot = count_real_and_bot(attacker_team_data, bot_names_set)

            nuanfu_status = "暖服结束" if defender_real >= 25 else "正在暖服"

            return {'success': True, 'defender_real': defender_real, 'defender_bot': defender_bot,
                    'attacker_real': attacker_real, 'attacker_bot': attacker_bot, 'nuanfu_status': nuanfu_status}
        else:
            logger.warning(f"check_nuanfu_status for {server_key}: async_get_players_gametools 返回无效数据。")
            return {'success': False, 'error_msg': "无法获取有效的玩家数据。"}
    except Exception as e:
        logger.error(f"check_nuanfu_status for {server_key} 失败: {str(e)}", exc_info=True)
        return {'success': False, 'error_msg': str(e)}

# 异步函数：监控暖服状态
async def monitor_nuanfu(server_key, user_id, group_id):
    # This task runs within the global_asyncio_loop thread
    logger.info(f"[{server_key}] 开始监控，每分钟检查，超时30分钟。")
    start_time = datetime.datetime.now()

    try:
        while True:
            # Check if task is cancelled before waiting
            if asyncio.current_task().cancelled():
                 raise asyncio.CancelledError()

            logger.debug(f"[{server_key}] 等待 60 秒...")
            await asyncio.sleep(60)
            logger.debug(f"[{server_key}] 等待结束，检查状态...")

            # Check if task is cancelled after waiting
            if asyncio.current_task().cancelled():
                 raise asyncio.CancelledError()

            current_time = datetime.datetime.now()
            elapsed_minutes = (current_time - start_time).total_seconds() / 60

            if elapsed_minutes > 30:
                logger.info(f"[{server_key}] 监控超时。")
                # Check if task is cancelled before sending message
                if asyncio.current_task().cancelled(): raise asyncio.CancelledError()
                await send_group_msg(group_id=group_id, at_someone=user_id, text=f"服务器 {server_key} 暖服监控超时30分钟，停止。")
                break # 超时时结束

            try:
                result = await check_nuanfu_status(server_key)

                # Check if task is cancelled after API call
                if asyncio.current_task().cancelled(): raise asyncio.CancelledError()

                if result['success']:
                    if result['nuanfu_status'] == "暖服结束":
                        logger.info(f"[{server_key}] 暖服已结束，停止监控。")
                        # Check if task is cancelled before sending message
                        if asyncio.current_task().cancelled(): raise asyncio.CancelledError()
                        await send_group_msg(group_id=group_id, at_someone=user_id, text=f"服务器 {server_key} 暖服已结束。监控停止。")
                        break # 暖服结束时结束
                    else:
                        # Still in warming up state, optionally send status update
                        nuanfu_status = result['nuanfu_status']
                        logger.info(f"[{server_key}] 当前暖服状态: {nuanfu_status}")
                        # Example: Send a message every 5 minutes if still warming up
                        # if int(elapsed_minutes) % 5 == 0:
                        #      await send_group_msg(group_id, at_someone=user_id, text=f"服务器 {server_key} 仍在暖服中...")
                        pass # Currently sends nothing until finished/timeout
                else:
                    error_msg = result['error_msg']
                    logger.error(f"[{server_key}] 检查暖服状态失败: {error_msg} - 监控将继续每分钟重试")
                    # Check if task is cancelled before sending message
                    if asyncio.current_task().cancelled(): raise asyncio.CancelledError()
                    #await send_group_msg(group_id=group_id, text=f"服务器 {server_key} 暖服监控检查失败: {error_msg} - 将在下一分钟重试。")
                    continue # API 失败时继续循环

            except Exception as e:
                logger.error(f"[{server_key}] 监控循环中异常: {str(e)}", exc_info=True)
                # Check if task is cancelled before sending message
                if asyncio.current_task().cancelled(): raise asyncio.CancelledError()
                await send_group_msg(group_id=group_id, text=f"服务器 {server_key} 暖服监控循环中发生错误，但将继续尝试每分钟检查。错误: {e}")
                continue # 异常时继续循环

    except asyncio.CancelledError:
        # This block is reached if the task is explicitly cancelled
        logger.warning(f"[{server_key}] 暖服监控任务被取消。")
        # Clean up dictionary inside the thread safe context (finally)
        # You could send a cancellation message here if desired
        # await send_group_msg(group_id, text=f"服务器 {server_key} 的暖服监控任务已被取消。")

    except Exception as e:
        # Catch any other unhandled exceptions that cause the task to fail
        logger.error(f"[{server_key}] 监控暖服状态发生未预期错误: {str(e)}", exc_info=True)
        # Check if task is cancelled before sending message
        if not asyncio.current_task().cancelled(): # Only send if not cancelled
             await send_group_msg(group_id=group_id, at_someone=user_id, text=f"服务器 {server_key} 监控暖服状态发生严重错误，监控停止。请联系管理员。错误: {e}")
        else:
             logger.warning(f"[{server_key}] 监控任务在发送错误消息前被取消。")

    finally:
        # This finally block will run when the coroutine finishes (break, return, exception, cancellation)
        logger.info(f"[{server_key}] finally 执行了 - 任务结束.")
        # Access monitoring_tasks safely from the asyncio thread
        if server_key in monitoring_tasks:
            del monitoring_tasks[server_key]
        logger.info(f"[{server_key}] 停止监控任务。")


# cmd_cnf
@register_command("cnf")
async def cmd_cnf(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
     # This command handler also runs within the global_asyncio_loop thread
     try:
         args_str = args.strip().lower()
         config = NIUNIUBOT_CONFIG # Access config

         if not args_str:
             # Access monitoring_tasks safely (reading keys should be ok)
             active_tasks = {k: v for k, v in monitoring_tasks.items() if not v.done()}
             if active_tasks:
                 monitoring_list = ', '.join(active_tasks.keys())
                 await send_group_msg(group_id=group_id, at_someone=user_id, text=f"当前正在监控的服务器: {monitoring_list}")
             else:
                 await send_group_msg(group_id=group_id, at_someone=user_id, text="当前没有服务器在监控中。")
         else:
             server_key = args_str
             our_server_config = config.get("our_server", {})
             if not isinstance(our_server_config, dict) or server_key not in our_server_config:
                 available_servers = ', '.join(our_server_config.keys()) if our_server_config else "无"
                 await send_group_msg(group_id=group_id, at_someone=user_id, text=f"无效的服务器号。当前可用服务器: {available_servers}")
                 return

             # Check if already monitoring and task is still running
             if server_key in monitoring_tasks:
                 task = monitoring_tasks[server_key]
                 if not task.done(): # Task is still running or scheduled
                     await send_group_msg(group_id=group_id, at_someone=user_id, text=f"服务器 {server_key} 已在监控中。")
                     return
                 else:
                     # Task is done (finished, cancelled, or exception)
                     # Optionally remove it if done, so a new one can be started
                     del monitoring_tasks[server_key]
                     logger.info(f"Removed finished monitoring task for {server_key}")


             # Create and start the new monitoring task on the current loop
             task = asyncio.create_task(monitor_nuanfu(server_key, user_id, group_id))
             monitoring_tasks[server_key] = task
             logger.info(f"已启动服务器 {server_key} 的暖服状态监控任务")
             await send_group_msg(group_id=group_id, at_someone=user_id, text=f"已开始监控服务器 {server_key} 的暖服状态。")

     except Exception as e:
         logger.error(f"cmd_cnf 命令执行失败: {str(e)}", exc_info=True)
         await send_group_msg(group_id=group_id, at_someone=user_id, text="命令执行失败，请联系管理员")


# scnf 命令 (Stop monitoring)
@register_command("scnf")
async def cmd_scnf(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
    # This command handler also runs within the global_asyncio_loop thread
    try:
        args_parts = args.strip().split()
        if len(args_parts) < 1:
            await send_group_msg(group_id=group_id, at_someone=user_id, text="请提供服务器号（例如 scnf s1）")
            return

        server_key = args_parts[0].lower()
        config = NIUNIUBOT_CONFIG

        our_server_config = config.get("our_server", {})
        if not isinstance(our_server_config, dict) or server_key not in our_server_config:
            available_servers = ', '.join(our_server_config.keys()) if our_server_config else "无"
            await send_group_msg(group_id=group_id, at_someone=user_id, text=f"无效的服务器号。当前可用服务器: {available_servers}")
            return

        # Safely access and cancel the task
        if server_key in monitoring_tasks:
            task = monitoring_tasks[server_key]
            if not task.done(): # Only cancel if it's not already finished
                task.cancel()
                # The task's finally block will remove it from monitoring_tasks
                # So we don't need to del it here immediately
                #wait send_group_msg(group_id=group_id, at_someone=user_id, text=f"正在停止监控服务器 {server_key}...")
                logger.info(f"发送取消信号给服务器 {server_key} 的监控任务")
                await asyncio.sleep(5)
                if server_key in monitoring_tasks:
                    await send_group_msg(group_id=group_id, at_someone=user_id,
                                         text=f"停止监控服务器 {server_key}失败")
                else:
                    await send_group_msg(group_id=group_id, at_someone=user_id,
                                         text=f"停止监控服务器 {server_key}成功")

            else:
                 # If the task is already done, just remove it from the dict if it's still there
                 if server_key in monitoring_tasks:
                      del monitoring_tasks[server_key]
                      logger.info(f"Removed already done monitoring task for {server_key}")
                 await send_group_msg(group_id=group_id, at_someone=user_id, text=f"服务器 {server_key} 没有在监控中。")
        else:
            await send_group_msg(group_id=group_id, at_someone=user_id, text=f"服务器 {server_key} 没有在监控中。")

    except Exception as e:
        logger.error(f"cmd_scnf 命令执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id=group_id, at_someone=user_id, text="命令执行失败，请联系管理员")


# snf 命令 (Check status once)
@register_command("snf")
async def cmd_snf(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
    # This command handler also runs within the global_asyncio_loop thread
    try:
        args_parts = args.strip().split()
        if len(args_parts) < 1:
            await send_group_msg(group_id=group_id, at_someone=user_id, text="请提供服务器号（例如 snf s1）")
            return

        server_key = args_parts[0].lower()

        # Call check_nuanfu_status function directly
        result = await check_nuanfu_status(server_key)

        if not result['success']:
            await send_group_msg(group_id=group_id, at_someone=user_id, text=result['error_msg'])
            return

        # Success case
        data = result
        nuanfu_status = data['nuanfu_status']
        summary_text = (f"\n{server_key} 暖服状态: {nuanfu_status}\n"
                        f"日军: 真人 {data['defender_real']}, 暖服机 {data['defender_bot']} \n"
                        f"美军: 真人 {data['attacker_real']}, 暖服机 {data['attacker_bot']} \n")
        await send_group_msg(group_id=group_id, at_someone=user_id, text=summary_text)

    except Exception as e:
        logger.error(f"cmd_snf 命令执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id=group_id, at_someone=user_id, text="命令执行失败，请联系管理员")

# knf 命令 (Kick bots)
@register_command("knf")
async def cmd_knf(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
    # This command handler also runs within the global_asyncio_loop thread
    try:
        if not is_admin:
            await send_group_msg(group_id=group_id, at_someone=user_id, text="权限不足。只有管理员才能执行此命令。")
            return

        # Load bot names using the function with lock
        bot_names_set = load_bot_names()
        if not bot_names_set:
             error_msg = "错误: 机器人名单为空或读取失败。请检查文件 nfj.txt。"
             logger.warning(error_msg)
             await send_group_msg(group_id=group_id, at_someone=user_id, text=error_msg)
             return


        args_parts = args.strip().split()
        if len(args_parts) < 1:
            await send_group_msg(group_id=group_id, at_someone=user_id, text="请提供服务器号（例如 knf s1）")
            return

        server_key = args_parts[0].lower()
        config = NIUNIUBOT_CONFIG

        our_server_config = config.get("our_server", {})
        if not isinstance(our_server_config, dict) or server_key not in our_server_config:
            available_servers = ', '.join(our_server_config.keys()) if our_server_config else "无"
            await send_group_msg(group_id=group_id, at_someone=user_id, text=f"无效的服务器号。当前可用服务器: {available_servers}")
            return

        game_id = our_server_config[server_key]

        # Get players data asynchronously
        players_data = await async_get_players_gametools(game_id=game_id)

        if not players_data or 'teams' not in players_data:
             await send_group_msg(group_id=group_id, at_someone=user_id, text="无法获取有效的玩家数据，无法执行踢出操作。")
             return

        all_players = []
        for team in players_data.get('teams', []):
             for player in team.get('players', []):
                 player_name = player.get('name', '').strip() # Get original name for kick command
                 if player_name:
                     all_players.append(player_name) # Add non-empty names


        # Find online bots using the loaded set (case-insensitive match)
        online_bots_names = [name for name in all_players if name.lower() in bot_names_set]


        if not online_bots_names:
            await send_group_msg(group_id=group_id, at_someone=user_id, text=f"服务器 {server_key} 中没有检测到在线机器人。")
            return

        # Prepare and send kick commands
        target_user_id = config.get("nfj_bot") # Assuming nfj_bot is the target to send kick commands to
        if not target_user_id:
             logger.error("cmd_knf: nfj_bot 配置缺失，无法发送踢出命令。")
             await send_group_msg(group_id=group_id, at_someone=user_id, text="系统错误: 未配置发送踢出命令的目标机器人。")
             return

        await send_group_msg(group_id=group_id, at_someone=user_id, text=f"正在向 {target_user_id} 发送踢出 {len(online_bots_names)} 个暖服机的命令...")

        success_count = 0
        failed_bots = []

        # Send commands sequentially with delay
        for bot_name in online_bots_names:
            kick_command = f"/kick {bot_name} 暖服机卡机" # Use original name for kick command
            try:
                # Send private message to the target bot ID
                # The target bot is assumed to be in a group where it can process /kick
                # Sending /kick in a group message to the target bot ID is more common
                # Let's assume sending to the nfj_group is correct based on nfj command
                nfj_group_id = config.get("nfj_group")
                if not nfj_group_id:
                     logger.error("cmd_knf: nfj_group 配置缺失，无法发送踢出命令。")
                     failed_bots.append(bot_name)
                     continue # Skip sending this command

                # Send the kick command in the nfj_group, mentioning the target bot ID
                # This assumes the target bot ID (nfj_bot) is a member of nfj_group
                sent_msg_id = await send_group_msg(group_id=nfj_group_id, text=kick_command, at_someone=target_user_id)

                if sent_msg_id is not None:
                    success_count += 1
                    logger.info(f"cmd_knf: 成功发送踢出命令到群组 {nfj_group_id}: {kick_command}")
                else:
                    logger.error(f"cmd_knf: 发送踢出命令失败 for {bot_name} to group {nfj_group_id}")
                    failed_bots.append(bot_name)

                # Add 1 second delay between commands
                await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"cmd_knf: 发送踢出命令失败 for {bot_name}: {str(e)}", exc_info=True)
                failed_bots.append(bot_name)
                # Continue to the next command even if one fails

        # Send summary message
        summary_message = f"尝试发送 {len(online_bots_names)} 个踢出命令。"
        if success_count > 0:
            summary_message += f" 成功发送 {success_count} 个。"
        if failed_bots:
            summary_message += f" 发送失败 {len(failed_bots)} 个: {', '.join(failed_bots)}。"

        await send_group_msg(group_id=group_id, at_someone=user_id, text=summary_message)

    except Exception as e:
        logger.error(f"cmd_knf 命令执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id=group_id, at_someone=user_id, text="命令执行失败，请联系管理员")


# HTTP回调接口，修改为将任务提交到全局 asyncio 循环
@app.route('/api/event/post', methods=['POST'])
def event_post():
    try:
        data = request.get_json()
        #logger.debug(f"收到事件: {data}") # Debug log can be noisy

        # Wait for the asyncio loop to be ready before submitting tasks
        if not asyncio_loop_ready.is_set():
             logger.warning("Asyncio loop is not yet ready, skipping message processing.")
             # Return accepted but not processed, or an error
             return jsonify({"status": "warning", "message": "Asyncio loop not ready"}), 503 # Service Unavailable


        if data.get("post_type") == "message":
            # Check if global_asyncio_loop is running and valid
            if global_asyncio_loop and global_asyncio_loop.is_running():
                # Submit the handle_message coroutine to the global asyncio loop
                # Use run_coroutine_threadsafe from a different thread (Flask thread)
                # wrap_future=False means it returns a concurrent.futures.Future
                # wrap_future=True (default) means it returns an asyncio.Future
                # We don't need the return value immediately, so either is fine, but wrap_future=False
                # might be slightly simpler if you never wait on it in the Flask thread.
                # Let's use the default (True) for standard asyncio Future handling.
                asyncio.run_coroutine_threadsafe(handle_message(data), global_asyncio_loop)

                return jsonify({"status": "ok", "message": "message processing submitted"})
            else:
                logger.error("Global asyncio loop is not running when receiving a message event!")
                # Return an error status indicating the backend isn't fully operational
                return jsonify({"status": "error", "message": "Asyncio loop not running"}), 500
        # For non-message post_types, just return ok
        return jsonify({"status": "ok"})

    except Exception as e:
        logger.error(f"处理事件失败: {e}", exc_info=True)
        return jsonify({"status": "failed", "error": str(e)}), 500


# 启动通知函数
def send_startup_notification():
    logger.info("转发机器人服务已启动")
    # You could also send a message to a group here if needed, but requires the asyncio loop
    # if global_asyncio_loop and global_asyncio_loop.is_running():
    #     asyncio.run_coroutine_threadsafe(
    #         send_group_msg(text="转发机器人服务已启动"),
    #         global_asyncio_loop
    #     )
    # else:
    #     logger.warning("Cannot send startup message, asyncio loop not ready.")


# 动态注册新命令：基于 our_server 的所有键
# This must happen AFTER NIUNIUBOT_CONFIG is loaded, and BEFORE app.run
def register_dynamic_commands(config):
     if "our_server" in config and isinstance(config["our_server"], dict) and config["our_server"]:
         current_server_group_name = config.get("server_group_name", "默认群组名")
         for key in list(config["our_server"].keys()):
             async def dynamic_playerlist_cmd(group_id, user_id, args, is_private=False, is_admin=False, message_data=None, cmd_key=key, srv_group_name=current_server_group_name):
                 # This dynamic command runs in the global asyncio loop thread
                 try:
                     # Assuming 3889013937 is the target bot ID for playerlist
                     await send_group_msg(group_id=group_id, text=f"/playerlist {srv_group_name} {cmd_key}",
                                          at_someone=3889013937)
                 except Exception as e:
                     logger.error(f"动态命令 '{cmd_key}' 执行失败: {str(e)}", exc_info=True)
                     if not is_private:
                         await send_group_msg(group_id=group_id, at_someone=user_id, text=f"命令 {cmd_key} 执行失败，请联系管理员")
             # 将新命令注册到 command_handlers
             command_handlers[key] = dynamic_playerlist_cmd
             logger.info(f"动态命令 '{key}' 注册成功")
     else:
         logger.warning("our_server 配置缺失、不是字典或为空，未注册动态命令")


if __name__ == '__main__':
     # 解析命令行参数
     parser = argparse.ArgumentParser(description="NiuNiuBot Server")
     parser.add_argument('-b', '--bind', default='127.0.0.1', help='绑定地址，默认127.0.0.1')
     parser.add_argument('-p', '--port', type=int, default=18889, help='监听端口，默认18889')
     args = parser.parse_args()

     logger.info(f"使用绑定地址: {args.bind}, 端口: {args.port}")

     # --- Start the dedicated asyncio loop in a separate thread ---
     asyncio_thread = threading.Thread(target=run_asyncio_in_thread)
     asyncio_thread.daemon = True # Allow the main thread to exit even if asyncio thread is running
     asyncio_thread.start()

     # Wait for the asyncio loop to be ready before proceeding
     logger.info("等待 asyncio 事件循环启动...")
     asyncio_loop_ready.wait(timeout=10) # Wait up to 10 seconds

     if not global_asyncio_loop or not global_asyncio_loop.is_running():
          logger.error("未能成功启动 asyncio 事件循环线程！机器人将无法处理异步任务。")
          # Optionally exit or proceed with limited functionality
          # exit(1) # Exit if asyncio is critical

     # --- Register dynamic commands (depends on loaded config) ---
     # This needs to be done after config is loaded but before starting the Flask app
     register_dynamic_commands(NIUNIUBOT_CONFIG)


     logger.info("正在启动 Flask web 服务...")

     # 启动 Flask 服务器，启用多线程模式
     # IMPORTANT: When using separate threads and asyncio, disable Flask's reloader
     # as it can cause the script to run twice and create multiple loops/threads.
     app.run(host=args.bind, port=args.port, threaded=True, debug=False, use_reloader=False)

     # The code below app.run will only be reached if the Flask server stops.
     logger.info("Flask web 服务已停止。")

     # Optional: Signal the asyncio thread to stop if Flask stops first
     # This is more complex and often handled by OS signals (like Ctrl+C) that
     # the asyncio loop should catch to set its stop event.
     # For simplicity, relying on daemon=True might be sufficient for basic shutdown.
     # To implement graceful shutdown, you'd add signal handlers in the main thread
     # or asyncio thread to set the stop_event used in main_asyncio_tasks.
