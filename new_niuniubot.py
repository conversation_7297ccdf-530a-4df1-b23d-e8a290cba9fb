###############################################################################
#                                   导入模块                                   #
###############################################################################
import difflib
import threading
import time
import os
import json
import re
import logging
from typing import Set, Optional
import aiofiles
import aiohttp  # 异步 HTTP 库
import requests
from flask import Flask, request, jsonify
import datetime
from math import ceil
import argparse
import logging.handlers
import asyncio

###############################################################################
#                                   日志配置                                   #
###############################################################################
# 自定义日志配置
handler_file = logging.handlers.RotatingFileHandler('niuniubot.log', maxBytes=20*1024*1024, backupCount=5, encoding='utf-8')
handler_file.setLevel(logging.DEBUG)
handler_file.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
handler_console = logging.StreamHandler()
handler_console.setLevel(logging.INFO)
handler_console.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
root_logger = logging.getLogger()
root_logger.addHandler(handler_file)
root_logger.addHandler(handler_console)
root_logger.setLevel(logging.DEBUG)
werkzeug_logger = logging.getLogger("werkzeug")
werkzeug_logger.setLevel(logging.WARNING)
logger = logging.getLogger(__name__)

###############################################################################
#                                 全局变量初始化                               #
###############################################################################
app = Flask(__name__)

# 创建全局锁，用于保护文件操作 (如config.json)
file_lock = threading.Lock()

# 加载配置从config.json文件
try:
    with open('config.json', 'r') as f:
        NIUNIUBOT_CONFIG = json.load(f)
    server_group_name = NIUNIUBOT_CONFIG.get("server_group_name", "默认群组名") # 添加默认值
    logger.info("NiuNiuBot配置加载成功")
except FileNotFoundError:
    logger.error("错误：config.json 文件未找到！请确保文件存在于当前目录。")
    NIUNIUBOT_CONFIG = {}
except json.JSONDecodeError:
    logger.error("错误：config.json 文件的JSON格式无效！请检查文件内容。")
    NIUNIUBOT_CONFIG = {}

# 新增全局变量，用于存储主 asyncio 事件循环
global_asyncio_loop = None
# 用于在主循环启动后发出信号
asyncio_loop_ready = threading.Event()
server_group_name = NIUNIUBOT_CONFIG.get("server_group_name", "默认群组名")

# 命令处理器字典
command_handlers = {}

# 注册命令装饰器
def register_command(command_name):
    def decorator(func):
        command_handlers[command_name] = func
        return func
    return decorator

###############################################################################
#                                 配置管理相关                                 #
###############################################################################

async def update_game_id():
    """更新游戏服务器ID"""
    url = f"https://api.bfvrobot.net/api/bfv/servers?serverName={server_group_name}&region=all&limit=200&lang=zh-CN"
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                response.raise_for_status()
                data = await response.json()
                if data.get('success') != 1:
                    logger.error(f"game_id API请求失败: {data.get('code')}")
                    return
                servers_data = data.get('data', [])
                if not isinstance(servers_data, list):
                    logger.error("game_id API响应数据格式错误: 'data' 不是列表")
                    return
    except aiohttp.ClientError as e:
        logger.error(f"game_id 请求API失败: {str(e)}", exc_info=True)
        return
    except json.JSONDecodeError as e:
        logger.error(f"game_id 解析API响应JSON失败: {str(e)}", exc_info=True)
        return

    try:
        # 确保在更新配置时使用文件锁，因为主线程（Flask启动前）和 asyncio 线程都可能加载配置
        with file_lock: # 使用文件锁
            with open('config.json', 'r', encoding='utf-8') as f: # 指定编码
                config = json.load(f)
            logger.info("config.json 加载成功")
    except FileNotFoundError:
        logger.error("错误：config.json 文件未找到！")
        return
    except json.JSONDecodeError:
        logger.error("错误：config.json 文件的JSON格式无效！使用空配置。")
        return

    our_server_config = config.get('our_server', {})
    if not isinstance(our_server_config, dict):
        logger.warning("our_server 不是字典格式，创建空字典。")
        our_server_config = {}

    updated = False
    for key in list(our_server_config.keys()):
        key_lower = key.lower()
        matching_server = None
        for server in servers_data:
            if isinstance(server, dict):
                server_name_lower = server.get('serverName', '').lower()
                if key_lower in server_name_lower:
                    matching_server = server
                    break
        if matching_server:
            new_game_id = matching_server.get('gameId')
            if new_game_id is not None:
                old_game_id = our_server_config.get(key)
                if str(new_game_id) != str(old_game_id):
                    our_server_config[key] = str(new_game_id)
                    logger.info(f"更新 {key} 的 gameId: {old_game_id} -> {new_game_id}")
                    updated = True
                else:
                    logger.info(f"{key} 的 gameId 未变化: {new_game_id}")
            else:
                logger.warning(f"服务器匹配到 {key} 但缺少 gameId 字段")
        else:
            logger.warning(f"未找到匹配的服务器: {key}")

    if updated:
        config['our_server'] = our_server_config
        try:
            with file_lock: # 使用文件锁
                with open('config.json', 'w', encoding='utf-8') as f: # 指定编码
                    json.dump(config, f, indent=4)
            logger.info("config.json 更新成功")
        except IOError as e:
            logger.error(f"保存 config.json 失败: {str(e)}")

    # 重新加载配置到 NIUNIUBOT_CONFIG
    # 确保在重新加载时使用文件锁
    try:
        with file_lock: # 使用文件锁
            with open('config.json', 'r', encoding='utf-8') as f: # 指定编码
                global NIUNIUBOT_CONFIG
                NIUNIUBOT_CONFIG = json.load(f)
        logger.info("NiuNiuBot 配置重新加载成功")
        # 重新设置全局的 server_group_name
    except FileNotFoundError:
        logger.error("错误：config.json 文件未找到！")
        NIUNIUBOT_CONFIG = {}
    except json.JSONDecodeError:
        logger.error("错误：config.json JSON格式无效！")
        NIUNIUBOT_CONFIG = {}


# 定义一个函数来打印配置为表格格式
def print_config_table(config):
    """打印配置为表格格式"""
    if not config:
        return "无配置可用。"
    # 处理配置可能不是字典的情况
    if not isinstance(config, dict):
        return "配置不是字典格式。"

    max_key_length = 0
    # 安全地遍历键，处理可能的非字符串键
    for key in config.keys():
        max_key_length = max(max_key_length, len(str(key)))

    table_str = f"{'Key'.ljust(max_key_length)} | Value\n"
    table_str += "-" * (max_key_length + 7) + "\n"

    for key, value in config.items():
        value_str = str(value)
        # 确保value_str不包含破坏表格格式的换行符
        value_str = value_str.replace('\n', '\\n')
        table_str += f"{str(key).ljust(max_key_length)} | {value_str}\n"
    return table_str

# 打印配置信息为表格格式 (在加载/重新加载后调用)
logger.info("当前的NiuNiuBot配置:\n" + print_config_table(NIUNIUBOT_CONFIG))

###############################################################################
#                                 工具函数相关                                 #
###############################################################################

DEFAULT_HELP_TEXT = "可用命令:\n显示帮助:nhelp\n呼叫暖服机命令:nfj 服务器序号(例:s1)\n举报语言攻击命令(ai自动判别):使用方法:回复你要举报的图片的信息 zc 服务器序号\n查看服务器列表 2"

def get_help_text(config):
    """获取帮助文本"""
    our_server_config = config.get("our_server", {})
    if not isinstance(our_server_config, dict):
        available_servers = "无"
    else:
        available_servers = ', '.join(our_server_config.keys())

    try:
        # 确保在读取文件时使用文件锁
        with file_lock:
            with open('help.txt', 'r', encoding='utf-8') as file:
                help_text = file.read().strip()
    except FileNotFoundError:
        logging.error("help.txt 文件未找到，请检查文件是否存在。")
        help_text = DEFAULT_HELP_TEXT
    except UnicodeDecodeError:
        logging.error("help.txt 文件编码错误，无法正确读取。")
        help_text = DEFAULT_HELP_TEXT
    except Exception as e:
        logging.error(f"读取help.txt文件时发生错误: {str(e)}")
        help_text = DEFAULT_HELP_TEXT

    full_help_text = f"欢迎来到{server_group_name}群组\n当前已有的服务器:{available_servers}\n{help_text}"
    return full_help_text

def calculate_next_reminder_time(now, start_hour, start_min, end_hour, end_min, interval_min):
    """计算下一次提醒时间"""

    start_time_dt = datetime.datetime.combine(now.date(), datetime.time(start_hour, start_min))
    end_time_dt = datetime.datetime.combine(now.date(), datetime.time(end_hour, end_min))
    if end_hour < start_hour or (end_hour == start_hour and end_min < start_min):
        end_time_dt += datetime.timedelta(days=1)
    interval_seconds = interval_min * 60
    if now < start_time_dt:
        next_time = start_time_dt
    else:
        time_since_start = (now - start_time_dt).total_seconds()
        # 确保间隔秒数不为零或负数
        if interval_seconds <= 0:
             logger.error(f"提醒间隔设置无效: {interval_min}分钟")
             # 返回一个遥远的未来时间以有效禁用此提醒
             return now + datetime.timedelta(days=365) # 返回一年后的时间

        intervals_passed = ceil(time_since_start / interval_seconds)
        next_time = start_time_dt + datetime.timedelta(seconds=intervals_passed * interval_seconds)

    # 如果next_time正好是结束时间，它应该是当天的最后一次提醒。
    # 如果超过结束时间，则计算下一天的时间。
    # 使用小的容差进行浮点比较更安全。
    if next_time > end_time_dt + datetime.timedelta(seconds=1): # 添加小的容差
        next_start = datetime.datetime.combine(now.date() + datetime.timedelta(days=1),
                                               datetime.time(start_hour, start_min))
        return next_start
    else:
        return next_time

###############################################################################
#                                 提醒系统相关                                 #
###############################################################################

async def reminder_loop():
    """提醒循环任务"""
    # 确保此任务在全局循环上运行
    logger.info("提醒任务启动。")
    await update_game_id() # 启动时初始更新

    while True:
        # 确保配置访问安全，如果配置可能会改变
        config = NIUNIUBOT_CONFIG # 假设配置已全局加载和更新

        if not config.get("reminder", False):
            logger.info("提醒功能未启用，暂停提醒任务。")
            # 如果提醒被禁用，在再次检查配置前休眠更长时间
            await asyncio.sleep(300) # 每5分钟检查一次配置
            continue # 回到循环开始

        # 安全地获取提醒设置，提供默认值
        reminder_set = config.get("reminder_set", {}) # 默认为空字典
        if not isinstance(reminder_set, dict):
            logger.error("reminder_set 配置无效，使用默认提醒时间。")
            reminder_set = {} # 使用下面的默认值

        start_hour = reminder_set.get("start_hour", 7)
        start_min = reminder_set.get("start_min", 1)
        end_hour = reminder_set.get("end_hour", 1)
        end_min = reminder_set.get("end_min", 1)
        interval_min = reminder_set.get("interval_min", 60)

        # 时间和间隔的基本验证
        if not (0 <= start_hour < 24 and 0 <= start_min < 60 and
                0 <= end_hour < 24 and 0 <= end_min < 60 and
                interval_min > 0):
             logger.error(f"提醒时间或间隔配置无效：start={start_hour}:{start_min}, end={end_hour}:{end_min}, interval={interval_min}分钟。暂停提醒。")
             await asyncio.sleep(300) # 再次检查配置前等待
             continue

        now = datetime.datetime.now()
        next_time = calculate_next_reminder_time(now, start_hour, start_min, end_hour, end_min, interval_min)

        wait_time = (next_time - now).total_seconds()
        # 确保等待时间不为负数（时钟变化或计算错误时可能发生）
        if wait_time < 0:
            wait_time = 1 # 如果时间在过去，至少等待1秒

        logger.info(f"下一次提醒时间: {next_time.strftime('%Y-%m-%d %H:%M:%S')} (等待 {int(wait_time)} 秒)")

        try:
            # 在异步函数中使用asyncio.sleep进行等待
            await asyncio.sleep(wait_time)
        except asyncio.CancelledError:
            logger.info("提醒任务被取消。")
            raise # 重新抛出异常让调用者知道

        # 在发送前确保当前时间在提醒窗口内
        # 重新计算当前时间并检查是否在发送窗口内
        current_check_time = datetime.datetime.now()
        start_time_today = datetime.datetime.combine(current_check_time.date(), datetime.time(start_hour, start_min))
        end_time_today = datetime.datetime.combine(current_check_time.date(), datetime.time(end_hour, end_min))
        if end_hour < start_hour or (end_hour == start_hour and end_min < start_min):
            # 结束时间在第二天
            if current_check_time >= start_time_today or current_check_time <= end_time_today:
                 # 在时间窗口内（今天开始时间之后或明天结束时间之前）
                 send_reminder = True
            else:
                 send_reminder = False
        else:
            # 结束时间在同一天
            if start_time_today <= current_check_time <= end_time_today:
                 send_reminder = True
            else:
                 send_reminder = False

        if send_reminder:
             # 发送提醒（help 文本）
             help_text = get_help_text(config) # 获取最新的帮助文本
             group_ids = config.get("reminder_groups", [])

             if not group_ids:
                 logger.warning("reminder_groups 配置为空，未发送提醒。")
                 # 即使没有群组也继续到下一个间隔
             else:
                 # 发送提醒前更新游戏ID
                 await update_game_id()
                 # 向每个群组发送提醒
                 for group_id in group_ids:
                     try:
                         await send_group_msg(group_id=group_id, text=help_text)
                         logger.info(f"已发送提醒到群组 {group_id}")
                     except Exception as e:
                         logger.error(f"发送提醒到群组 {group_id} 失败: {str(e)}")
        else:
             logger.info(f"当前时间 {current_check_time.strftime('%H:%M')} 不在提醒时间段内，跳过发送。")

###############################################################################
#                                异步循环管理                                 #
###############################################################################

def run_asyncio_in_thread():
    """运行 asyncio 事件循环的线程函数"""
    # 在新线程中创建和设置新的事件循环
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    # 将循环存储到全局变量，供其他线程访问
    global global_asyncio_loop
    global_asyncio_loop = loop

    logger.info("Asyncio 事件循环线程启动。")

    # 启动主 asyncio 逻辑（包括后台任务，如提醒）
    # 使用 loop.run_until_complete 来运行一个顶级协程，这个协程可以启动其他任务
    async def main_asyncio_tasks():
        logger.info("主 asyncio 任务启动。")
        # 启动提醒任务作为后台任务
        if NIUNIUBOT_CONFIG.get("reminder", False):
             asyncio.create_task(reminder_loop())
             logger.info("提醒循环任务已在主循环中启动。")

        # 添加一个Future来保持循环无限期运行直到被取消
        # 如果其他任务可能完成，但你希望循环保持活跃直到整个应用程序关闭，这是必要的
        stop_event = asyncio.Event() # 使用事件来信号关闭
        asyncio_loop_ready.set() # 信号循环已准备就绪

        try:
            # 等待直到stop_event被设置（例如，通过关闭信号）
            await stop_event.wait()
        except asyncio.CancelledError:
            logger.info("主 asyncio 任务收到取消信号。")
        except Exception as e:
            logger.error(f"主 asyncio 任务发生未预期错误: {e}", exc_info=True)
        finally:
            logger.info("主 asyncio 任务正在关闭所有子任务...")
            # 取消所有正在运行的任务，除了当前执行此finally块的任务
            tasks = [t for t in asyncio.all_tasks(loop=loop) if t is not asyncio.current_task(loop=loop)]
            for task in tasks:
                 task.cancel()
            # 等待任务完成或被取消
            await asyncio.gather(*tasks, return_exceptions=True)
            logger.info("所有 asyncio 子任务已关闭。")


    try:
        # 运行主 asyncio 任务，这将启动其他后台任务并保持循环运行
        loop.run_until_complete(main_asyncio_tasks())
    except asyncio.CancelledError:
        logger.info("Asyncio 循环线程被取消。")
    except Exception as e:
        logger.error(f"Asyncio 循环线程错误: {e}", exc_info=True)
    finally:
        logger.info("Asyncio 事件循环线程停止。")
        # 清理全局变量和循环
        global_asyncio_loop = None
        loop.close()
        logger.info("Asyncio 事件循环已关闭。")

###############################################################################
#                                 API相关函数                                 #
###############################################################################

async def get_message_detail(message_id, config):
    """获取消息详情（基于OpenAPI规范，异步版本）"""
    base_url = config.get("address", "")
    access_token = config.get("token", "")
    if not base_url:
        raise ValueError("API配置缺失，请检查config.json中的address")
    url = f"{base_url}/get_msg"
    payload = {"message_id": message_id}
    params_dict = {"access_token": access_token} if access_token else {}
    async with aiohttp.ClientSession() as session:
         try:
             async with session.post(url, json=payload, params=params_dict, timeout=10) as response:
                 response.raise_for_status()
                 data = await response.json()
                 if data.get("status") == "ok":
                     return data.get("data", {})
                 else:
                     raise Exception(f"API错误: {data.get('message')}, 错误码: {data.get('retcode')}")
         except aiohttp.ClientError as e:
             raise Exception(f"网络请求失败: {str(e)}")
         except Exception as e:
             raise Exception(f"获取消息详情失败: {str(e)}")

# 异步版本，使用 run_in_executor 运行同步函数
async def async_get_players_gametools(game_id=None):
    # 获取当前运行的循环（应该是此线程中的global_asyncio_loop）
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, get_players_gametools, game_id)

# 同步版本 get_players_gametools（保持不变）
def get_players_gametools(game_id=None):

     if game_id is None:
         logger.error("game_id 为空，无法获取玩家列表。")
         return None
     url = f"https://api.gametools.network/bfv/players/?gameid={game_id}"
     max_retries = 3
     timeout = 15  # 适当增加超时时间
     retry_delay = 2 # 适当增加重试延迟
     for attempt in range(1, max_retries + 1):
         try:
             # 发送GET请求，并设置超时
             response = requests.get(url, timeout=timeout)
             if response.status_code == 200:
                 # 如果成功，解析JSON数据
                 data = response.json()
                 # 简单检查数据结构
                 if isinstance(data, dict) and 'teams' in data:
                     return data
                 else:
                     logger.error(f"尝试 {attempt}: 服务器玩家列表API返回数据结构异常。")
                     return None # 返回 None 表示数据无效
             else:
                 logger.error(f"尝试 {attempt}: 服务器玩家列表请求失败，状态码: {response.status_code}")
                 if attempt < max_retries:
                     logger.info(f"等待 {retry_delay} 秒后重试...")
                     time.sleep(retry_delay)
         except requests.exceptions.RequestException as e:
             logger.error(f"尝试 {attempt}: 服务器玩家列表请求异常: {e}", exc_info=True)
             if attempt < max_retries:
                 logger.info(f"等待 {retry_delay} 秒后重试...")
                 time.sleep(retry_delay)
     logger.error("服务器玩家列表所有重试失败，无法获取数据。")
     return None




async def check_rsp(msg_id, group_id=None, match_condition=None):

     if not isinstance(msg_id, int):
         logger.error(f"check_rsp: msg_id '{msg_id}' 不是有效的整数，无法继续。")
         return [{"type": "text", "data": {"text": f"{msg_id}不是有效的整数"}}]

     config = NIUNIUBOT_CONFIG # Access config

     bot_qq = config.get("qq_bot", "")
     if not bot_qq:
         logger.error("check_rsp: qq_bot 配置缺失，请检查config.json")
         return [{"type": "text", "data": {"text": "配置错误: qq_bot 未设置"}}]

     card = ''
     try:
         # 使用全局循环进行等待
         bot_data = await asyncio.wait_for(get_group_member_info(group_id=group_id, user_id=bot_qq), timeout=5)
         card = bot_data.get('card', '')
     except asyncio.TimeoutError:
         logger.error("check_rsp: get_group_member_info 调用超时，机器人昵称设置为默认空值")
     except Exception as e:
         logger.error(f"check_rsp: 获取机器人昵称失败: {str(e)}，机器人昵称设置为默认空值", exc_info=True)

     if match_condition is None:
         server_group_name_lower = config.get("server_group_name", "").lower() # 从配置获取server_group_name
         bot_qq_str = str(bot_qq) # 将bot_qq转换为字符串进行比较

         def default_match(msg):
             raw_message_lower = msg.get('raw_message', '').lower()
             # 检查server_group_name（不区分大小写）或@bot
             # 使用CQ代码检查@bot比在raw_message中检查card/nickname更可靠
             return (server_group_name_lower in raw_message_lower and server_group_name_lower != "") or \
                    (f'[cq:at,qq={bot_qq_str}]' in raw_message_lower or \
                     f'[cq:at,qq=-{bot_qq_str}]' in raw_message_lower) # 考虑不同机器人类型/客户端的负QQ？


         match_func = default_match
     else:
         match_func = match_condition

     max_attempts = 10
     attempt = 0
     while attempt < max_attempts:
         try:
             # 获取消息历史，从seq 0（最新）开始最多20条消息
             # 使用全局循环进行等待
             msg_data = await asyncio.wait_for(get_group_msg_history(group_id=group_id, message_seq=0, count=20),
                                               timeout=10) # 稍微增加超时时间
             messages = msg_data.get('messages', [])

             found_target_msg = False
             for msg in messages:
                 # 确保message_id正确比较（可能是int或str）
                 if str(msg.get('message_id')) == str(msg_id):
                     found_target_msg = True
                     continue

                 if found_target_msg:
                     # 使用match_func检查消息是否为响应
                     if match_func(msg):
                         # 如果匹配则解析消息内容
                         message_array = []
                         for item in msg.get('message', []):
                              item_type = item.get('type')
                              item_data = item.get('data', {})
                              if item_type == 'image':
                                   image_url = item_data.get('url')
                                   if image_url:
                                        message_array.append({"type": "image", "data": {"file": image_url}}) # 在send_group_msg中使用"file"还是"url"？检查send_group_msg
                                        # send_group_msg在示例中对本地路径和url都使用"file"
                                        # 为了与send_group_msg保持一致，坚持使用带url字符串的"file"
                                        # message_array.append({"type": "image", "data": {"file": image_url}}) # 有时按照CQHTTP/go-cqhttp规范使用'url'键
                                        # 重新检查send_group_msg：它对文件路径和url都使用'file'。好的。
                                        message_array.append({"type": "image", "data": {"file": image_url}})
                              elif item_type == 'text':
                                   text_content = item_data.get('text', '')
                                   # 如果card可用，清理@bot提及
                                   if card and f'@{card}' in text_content:
                                       # 使用正则表达式进行更强大的替换，考虑潜在的前导/尾随空格
                                       cleaned_text = re.sub(rf'\s*@{re.escape(card)}\s*', '', text_content).strip()
                                   else:
                                       cleaned_text = text_content.strip() # 仍然去除前导/尾随空格

                                   # 如果去除空格后不为空则添加清理后的文本
                                   if cleaned_text:
                                       message_array.append({"type": "text", "data": {"text": cleaned_text + "\n"}}) # 这里删除了换行符，让发送者管理换行符

                              # 如果需要，添加其他类型，例如face、reply等
                              # 现在，只处理text和image，因为它们似乎与响应相关

                         # 找到正确的响应消息
                         logger.info(f"check_rsp: 找到并解析第一个响应消息: message_id {msg.get('message_id')}")
                         return message_array

             # 如果循环结束时在target_msg之后没有找到匹配项
             attempt += 1
             if attempt < max_attempts:
                 logger.info(f"check_rsp: 第 {attempt} 次尝试未找到响应消息 (msg_id {msg_id})，等待3秒后重试...")
                 await asyncio.sleep(3)

         except asyncio.CancelledError:
             logger.warning(f"check_rsp: 任务被取消 while waiting for msg_id {msg_id}")
             raise # 重新抛出取消异常

         except Exception as e:
             logger.error(f"check_rsp 错误 while waiting for msg_id {msg_id}: {str(e)}", exc_info=True)
             attempt += 1
             if attempt < max_attempts:
                 logger.info(f"check_rsp: 错误后等待3秒后重试...")
                 await asyncio.sleep(3)

     # 达到最大尝试次数
     logger.info(f"check_rsp: 最大尝试次数已达上限，未找到响应消息 (msg_id {msg_id})。")
     return [{"type": "text", "data": {"text": f"\n{3*max_attempts}秒内未找到指定返回消息，响应失败\n"}}]


# 异步 get_group_msg_history 函数
async def get_group_msg_history(group_id=None, message_seq=None, count=None, reverseorder=False, access_token=None):

     base_url = NIUNIUBOT_CONFIG.get("address", "")
     if not base_url:
         raise ValueError("get_group_msg_history: base_url 配置缺失，请检查config.json")

     # 使用提供的group_id或配置值
     group_id_to_use = group_id
     if group_id_to_use is None:
          # 如果group_id为None则回退到public_groups，但public_groups可能是列表
          # 此函数期望单个group_id。如果是列表则使用第一个。
          cfg_group = NIUNIUBOT_CONFIG.get("public_groups")
          if isinstance(cfg_group, list) and cfg_group:
               group_id_to_use = cfg_group[0]
          elif isinstance(cfg_group, (int, str)):
               group_id_to_use = cfg_group
          else:
               raise ValueError("get_group_msg_history: group_id 或 public_groups 配置无效")


     token_to_use = access_token or NIUNIUBOT_CONFIG.get("token", "") # token 可选

     if not all([group_id_to_use is not None, message_seq is not None, count is not None]):
         raise ValueError("get_group_msg_history: group_id, message_seq, count 是必填参数")

     payload = {
         "group_id": str(group_id_to_use), # 确保group_id是字符串
         "message_seq": str(message_seq),   # 确保seq是字符串
         "count": count,
         "reverseOrder": reverseorder
     }
     params_dict = {"access_token": token_to_use} if token_to_use else {}

     async with aiohttp.ClientSession() as session:
         try:
             # 如果消息历史很大或连接很慢，调整超时时间
             timeout = aiohttp.ClientTimeout(total=15) # 示例：15秒超时
             async with session.post(f"{base_url}/get_group_msg_history", json=payload, params=params_dict, timeout=timeout) as response:
                 response.raise_for_status()
                 data = await response.json()
                 if data.get("status") == "ok":
                     return data.get("data", {})
                 else:
                     logger.error(f"get_group_msg_history API 错误: {data.get('message')}, 错误码: {data.get('retcode')}")
                     raise Exception(f"API 错误: {data.get('message')}, 错误码: {data.get('retcode')}")
         except aiohttp.ClientError as e:
             logger.error(f"get_group_msg_history 网络请求失败: {str(e)}", exc_info=True)
             raise Exception(f"网络请求失败: {str(e)}")
         except Exception as e:
             logger.error(f"get_group_msg_history 错误: {str(e)}", exc_info=True)
             raise Exception(f"get_group_msg_history 错误: {str(e)}")


# 异步 get_group_member_info 函数
async def get_group_member_info(group_id=None, user_id=None, access_token=None):

     base_url = NIUNIUBOT_CONFIG.get("address", "")
     if not base_url:
         raise ValueError("get_group_member_info: base_url 配置缺失，请检查config.json")

     # 使用提供的group_id/user_id或配置值
     group_id_to_use = group_id or NIUNIUBOT_CONFIG.get("nfj_group") # nfj_group的回退
     user_id_to_use = user_id or NIUNIUBOT_CONFIG.get("qq_bot") # qq_bot的回退
     token_to_use = access_token or NIUNIUBOT_CONFIG.get("token", "") # token 可选

     if not all([group_id_to_use is not None, user_id_to_use is not None]):
         raise ValueError("get_group_member_info: group_id 和 user_id 是必填参数或配置项")

     payload = {"group_id": str(group_id_to_use), "user_id": str(user_id_to_use), "no_cache": True} # 确保字符串类型
     params_dict = {"access_token": token_to_use} if token_to_use else {}

     async with aiohttp.ClientSession() as session:
         try:
             timeout = aiohttp.ClientTimeout(total=10)
             async with session.post(f"{base_url}/get_group_member_info", json=payload, params=params_dict, timeout=timeout) as response:
                 response.raise_for_status()
                 data = await response.json()
                 if data.get("status") == "ok":
                     return data.get("data", {})
                 else:
                      logger.error(f"get_group_member_info API 错误: {data.get('message')}, 错误码: {data.get('retcode')}")
                      # 根据期望的行为返回空字典或抛出异常
                      return {} # 在API错误时返回空字典以优雅处理缺失信息
         except aiohttp.ClientError as e:
             logger.error(f"get_group_member_info 网络请求失败: {str(e)}", exc_info=True)
             # 在网络错误时返回空字典
             return {}
         except Exception as e:
             logger.error(f"get_group_member_info 错误: {str(e)}", exc_info=True)
             # 在其他错误时返回空字典
             return {}


# 异步 send_group_msg 函数
async def send_group_msg(group_id=None, text=None, image_file=None, image_url=None, at_someone=None, reply_some=None, json_data=None, access_token=None):

     base_url = NIUNIUBOT_CONFIG.get("address", "")
     if not base_url:
         logger.error("send_group_msg: base_url 配置缺失，请检查config.json")
         return None # 返回None或抛出错误

     # 使用提供的group_id，如果为None，使用public_groups配置（可以是列表或单个）
     # 如果public_groups是列表，发送给所有；如果是单个，发送给一个
     target_group_ids = []
     if group_id is not None:
         target_group_ids = [str(group_id)] # 确保它是字符串列表
     else:
         cfg_groups = NIUNIUBOT_CONFIG.get("public_groups")
         if isinstance(cfg_groups, list):
             target_group_ids = [str(g) for g in cfg_groups]
         elif isinstance(cfg_groups, (int, str)):
             target_group_ids = [str(cfg_groups)]
         else:
             logger.error("send_group_msg: group_id 参数为空且 public_groups 配置无效")
             return None

     if not target_group_ids:
          logger.error("send_group_msg: 没有指定群组ID或 public_groups 配置为空")
          return None

     token_to_use = access_token or NIUNIUBOT_CONFIG.get("token", "") # token 可选

     message_array = []
     # 如果发送图片则使用更长的超时时间
     timeout_total = 30 if (image_file or image_url) else 10

     if reply_some:
         message_array.append({"type": "reply", "data": {"id": str(reply_some)}}) # 确保回复ID是字符串

     # 如果提供了json_data则首先添加（允许自定义元素）
     if json_data:
         if isinstance(json_data, list): # 确保json_data是消息段列表
              for item in json_data:
                  # 如果已经由reply_some处理，避免再次添加回复段
                  if item.get("type") != "reply":
                      message_array.append(item)
         else:
              logger.warning("send_group_msg: json_data 不是列表格式，忽略。")


     if at_someone is not None:
         message_array.append({"type": "at", "data": {"qq": str(at_someone)}}) # 确保at_someone是字符串

     # 如果两者都存在，在@后添加文本段，带空格
     text_content = text
     if text_content:
         # 如果文本存在且最后一段是@，添加前导空格
         # 这防止"@user文本"并使其成为"@user 文本"
         # 但是，当前代码在@之后添加文本，所以空格已经在cmd_help中添加了
         # 让我们保持逻辑简单，如果需要在这里添加空格。
         # 简化：只添加文本。如果添加了@，下一个文本段自然跟随。
         # 原始代码如果at_someone存在则添加f" {text}"。
         # 让我们复制该逻辑或简化。始终将文本作为单独段添加更清洁。
         message_array.append({"type": "text", "data": {"text": " "+text_content}})


     if image_file:
         abs_image_file = os.path.abspath(image_file)
         if not os.path.exists(abs_image_file):
             logger.error(f"send_group_msg: 图片文件不存在: {abs_image_file}")
             # 决定行为：抛出错误还是跳过图片？让我们抛出。
             raise FileNotFoundError(f"图片文件不存在: {abs_image_file}")
         message_array.append({"type": "image", "data": {"file": f"file://{abs_image_file}"}}) # 对本地路径使用file://

     if image_url:
         # 根据go-cqhttp，对外部URL使用'url'键
         message_array.append({"type": "image", "data": {"url": image_url}})
         # 如果你的CQHTTP版本需要对URL使用'file'，在这里将'url'改为'file'。
         #message_array.append({"type": "image", "data": {"file": image_url}}) # 某些客户端的替代方案


     if not message_array:
         logger.warning("send_group_msg: 没有提供任何消息内容，跳过发送。")
         return None # 没有内容要发送

     # 向所有目标群组发送消息
     message_ids = []
     for target_group_id in target_group_ids:
         payload = {"group_id": target_group_id, "message": message_array}
         params_dict = {"access_token": token_to_use} if token_to_use else {}
         try:
             async with aiohttp.ClientSession() as session:
                 # 使用ClientTimeout对象
                 timeout = aiohttp.ClientTimeout(total=timeout_total)
                 async with session.post(f"{base_url}/send_group_msg", json=payload, params=params_dict, timeout=timeout) as response:
                     response.raise_for_status()
                     data = await response.json()
                     if data.get("status") == "ok":
                         sent_message_id = data["data"].get("message_id")
                         if sent_message_id:
                             message_ids.append(sent_message_id)
                         else:
                             logger.warning(f"send_group_msg: 发送到群组 {target_group_id} 成功但未返回 message_id。")
                     else:
                         logger.error(f"send_group_msg: 发送到群组 {target_group_id} 失败: {data.get('message')}, 错误码: {data.get('retcode')}")
         except aiohttp.ClientError as e:
             logger.error(f"send_group_msg: 发送群消息到 {target_group_id} 失败: {str(e)}", exc_info=True)
         except Exception as e:
              logger.error(f"send_group_msg: 发送群消息到 {target_group_id} 发生未知错误: {str(e)}", exc_info=True)


     # 如果发送了多条消息，返回ID列表。如果一条，返回该ID。
     # 如果没有成功发送，返回None。
     if len(message_ids) == 1:
         return message_ids[0]
     elif len(message_ids) > 1:
         return message_ids # 返回列表
     else:
         return None # 发送到任何群组都失败


# 异步 send_private_msg 函数
async def send_private_msg(user_id, text, access_token=None):

     base_url = NIUNIUBOT_CONFIG.get("address", "")
     if not base_url:
         logger.error("send_private_msg: base_url 配置缺失，请检查config.json")
         return None

     token_to_use = access_token or NIUNIUBOT_CONFIG.get("token", "") # token 可选

     if user_id is None or not text:
         logger.error("send_private_msg: user_id 或 text 参数缺失")
         return None

     # 消息应该是消息数组
     message_array = [{"type": "text", "data": {"text": text}}]

     payload = {"user_id": str(user_id), "message": message_array} # 确保user_id是字符串
     params_dict = {"access_token": token_to_use} if token_to_use else {}

     async with aiohttp.ClientSession() as session:
         try:
             timeout = aiohttp.ClientTimeout(total=10)
             async with session.post(f"{base_url}/send_private_msg", json=payload, params=params_dict, timeout=timeout) as response:
                 response.raise_for_status()
                 data = await response.json()
                 if data.get("status") == "ok":
                     # 私聊消息通常在data中返回message_id，而不是data['data']
                     return data.get("data", {}).get("message_id")
                 else:
                     logger.error(f"send_private_msg API 错误: {data.get('message')}, 错误码: {data.get('retcode')}")
                     return None
         except aiohttp.ClientError as e:
             logger.error(f"send_private_msg 网络请求失败: {str(e)}", exc_info=True)
             return None
         except Exception as e:
             logger.error(f"send_private_msg 错误: {str(e)}", exc_info=True)
             return None




# 修改后的 handle_message 函数：使用 message 数组解析命令
async def handle_message(message_data):
     # 此函数现在在global_asyncio_loop线程内运行
     try:
         logger.info(f"接收到消息:{message_data}")
         message_type = message_data.get("post_type") # 根据日志从message_type改为post_type
         if message_type != "message": # 只处理消息事件
             return

         # 根据实际message_type获取group_id或user_id
         group_id = message_data.get("group_id")
         user_id = message_data.get("user_id") # 发送者的user_id
         is_private = (message_data.get("message_type") == "private") # 检查message_type字段以区分私聊/群聊

         # 检查消息是否来自允许的群组
         if not is_private and group_id not in NIUNIUBOT_CONFIG.get("active_groups", []):
             return

         # 新增：提取发送者角色并根据message_data结构计算is_admin
         sender = message_data.get("sender", {})
         # 对于群消息，检查角色。对于私聊，检查发送者是否为admin_qq
         if is_private:
              admin_qq = str(NIUNIUBOT_CONFIG.get("admin_qq", "")) # 从配置获取admin_qq
              # 如果配置了admin_qq，来自admin_qq的私聊消息被视为管理员
              is_admin = (str(user_id) == admin_qq and admin_qq != "")
         else: # 群消息
              role = sender.get("role", "member") # 默认角色是'member'
              is_admin = (role == "owner" or role == "admin") # 群主或管理员有管理员权限

         # 解析消息数组，提取所有文本内容
         message_array = message_data.get("message", [])
         raw_text = ""
         for item in message_array:
             if item.get("type") == "text":
                 raw_text += item["data"].get("text", "") # 提取文本部分，暂时保留内部空格

         # 从整个文本块中删除潜在的前导/尾随空白
         raw_text = raw_text.strip()

         # 检查文本是否以命令前缀开头（例如'/'、'!'或根据设置无前缀）
         # 假设命令在消息开头像"command args"
         if raw_text:
             command_parts = raw_text.split(maxsplit=1)
             if command_parts:
                 command = command_parts[0].lower()
                 args = command_parts[1] if len(command_parts) > 1 else ""

                 if command in command_handlers:
                      logger.info(f"接收到命令: {command}, 参数: {args}, from user: {user_id}, group: {group_id}")
                      # 向命令处理器传递必要信息
                      await command_handlers[command](group_id, user_id, args, is_private=is_private, is_admin=is_admin,
                                                      message_data=message_data)
     except Exception as e:
         logger.error(f"处理消息失败: {str(e)}", exc_info=True)


# 注册命令处理函数
###############################################################################
#                                 基础命令相关                                 #
###############################################################################

# nhelp 命令
@register_command("nhelp")
async def cmd_help(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
     help_text = get_help_text(NIUNIUBOT_CONFIG)
     try:
         if is_private:
             await send_private_msg(user_id, help_text)
         else:
             # Ensure at_someone is used correctly with send_group_msg
             await send_group_msg(group_id=group_id, text=help_text, at_someone=user_id)
     except Exception as e:
         logger.error(f"cmd_help 执行失败: {str(e)}", exc_info=True)
         if not is_private: # Only respond in group if it failed in group
             await send_group_msg(group_id=group_id, text="获取帮助信息失败。", at_someone=user_id)


# 2 (服务器列表)
@register_command("2")
async def cmd_server(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
     try:
         # 假设3889013937是提供服务器列表的机器人的QQ号
         # 确保正确访问server_group_name
         current_server_group_name = NIUNIUBOT_CONFIG.get("server_group_name", "默认群组名")
         await send_group_msg(group_id=group_id, text=f"/server {current_server_group_name}", at_someone=3889013937)
     except Exception as e:
         logger.error(f"cmd_server 执行失败: {str(e)}", exc_info=True)
         if not is_private:
             await send_group_msg(group_id=group_id, text="查询服务器列表失败。", at_someone=user_id)


# get_gameid_by_name (辅助函数)
async def get_gameid_by_name(server_name):

    if server_name is None or server_name.strip() == "":
        return {"success": False, "result": "找不到服务器，请纠正查询参数"} # Return dict directly

    url = f"https://api.gametools.network/bfv/servers/?name={server_name}"
    max_retries = 3
    timeout = 10 # Increased timeout
    retry_delay = 2 # Increased retry delay

    for attempt in range(1, max_retries + 1):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=timeout) as response:
                    if response.status == 200:
                        try:
                            data = await response.json()
                            servers = data.get('servers', [])
                            if len(servers) == 1:
                                game_id = servers[0].get('gameId', None)
                                if game_id is not None:
                                    return {"success": True, "result": str(game_id)} # Ensure game_id is string
                                else:
                                    return {"success": False, "result": "在返回数据中找不到gameid"}
                            else:
                                # Return names of multiple servers found for better user feedback
                                found_names = ", ".join([s.get('serverName', '未知') for s in servers if isinstance(s, dict)])
                                return {"success": False, "result": f"找到多个匹配的服务器: {found_names}，请纠正查询参数"}

                        except json.JSONDecodeError as e:
                            error_msg = f"JSON decode error: {str(e)}"
                            logger.error(f"get_gameid_by_name 尝试 {attempt}: {error_msg}", exc_info=True)
                            if attempt < max_retries:
                                logger.info(f"get_gameid_by_name 等待 {retry_delay} 秒后重试...")
                                await asyncio.sleep(retry_delay)
                            continue # Continue to next attempt

                    else:
                        # HTTP status code not 200
                        error_msg = f"HTTP error with status code: {response.status}"
                        logger.error(f"get_gameid_by_name 尝试 {attempt}: {error_msg}")
                        if attempt < max_retries:
                            logger.info(f"get_gameid_by_name 等待 {retry_delay} 秒后重试...")
                            await asyncio.sleep(retry_delay)
                        continue # Continue to next attempt

        except aiohttp.ClientError as e:
            error_msg = f"Request exception: {str(e)}"
            logger.error(f"get_gameid_by_name 尝试 {attempt}: {error_msg}", exc_info=True)
            if attempt < max_retries:
                logger.info(f"get_gameid_by_name 等待 {retry_delay} 秒后重试...")
                await asyncio.sleep(retry_delay)
            continue # Continue to next attempt

    logger.error("get_gameid_by_name 所有尝试都失败了。")
    return {"success": False, "result": "所有尝试都失败了，请重试或联系管理员"} # Return dict on final failure


# /stop
@register_command("/stop")
async def cmd_stop_nfj(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):

     try:
         config = NIUNIUBOT_CONFIG # 访问配置
         if not config.get("nfj_stop", False): # 直接检查布尔值
             logger.info("/stop 命令已禁用。")
             if not is_private:
                  await send_group_msg(group_id, at_someone=user_id, text="stop 命令已禁用。")
             return

         # 如果配置了，检查命令是否来自允许的群组
         nfj_group_id = config.get("nfj_group")
         if nfj_group_id is not None and str(group_id) != str(nfj_group_id) and not is_private:
              logger.warning(f"/stop 命令在不允许的群组 {group_id} 执行。")
              if not is_private:
                   await send_group_msg(group_id, at_someone=user_id, text="此命令只能在特定群组或私聊中使用。")
              return

         # 检查args是否为数字且长度为14
         args_stripped = args.strip()
         if args_stripped.isdigit() and len(args_stripped) == 14:
             game_id = args_stripped
             logger.info(f"接收到 /stop 命令带 game_id: {game_id}")
             # 假设3889013937是目标机器人ID
             await send_group_msg(group_id=group_id, text=f"/stop {game_id}", at_someone=3889013937)
         else:
             # 调用异步get_gameid_by_name并获取字典结果
             logger.info(f"接收到 /stop 命令带服务器名: {args_stripped}")
             result_data = await get_gameid_by_name(args_stripped)

             if result_data.get("success"):
                 game_id = result_data.get("result")
                 logger.info(f"找到 game_id: {game_id}，发送 /stop 命令。")
                 # 假设3889013937是目标机器人ID
                 await send_group_msg(group_id=group_id, text=f"/stop {game_id}", at_someone=3889013937)
             else:
                 error_msg = result_data.get("result", "未知错误")
                 logger.warning(f"/stop 命令查找 game_id 失败: {error_msg}")
                 if not is_private:
                      await send_group_msg(group_id, at_someone=user_id, text=f"stop命令执行失败: {error_msg}")
                 else:
                      await send_private_msg(user_id, text=f"stop命令执行失败: {error_msg}")

     except Exception as e:
         logger.error(f"/stop 命令执行失败: {str(e)}", exc_info=True)
         error_response = "stop 命令执行失败，请联系管理员"
         if not is_private:
             await send_group_msg(group_id, at_someone=user_id, text=error_response)
         else:
             await send_private_msg(user_id, text=error_response)


###############################################################################
#                                 管理员命令相关                               #
###############################################################################

# 检查卡片是否在群成员中的辅助函数
async def check_card_in_members(card, members):
    """
    异步检查成员列表中是否存在指定的群昵称（card），使用不区分大小写的子字符串匹配。
    参数:
    - card: 要检查的群昵称（字符串），例如 "ccyniuniu"。会进行子字符串匹配（例如 "管理Ccyniuniu" 能被匹配）。
    - members: 成员列表（数组），由 get_group_member_list 函数返回。
    返回:
    - True: 如果昵称（或其子字符串）存在于成员列表中（不区分大小写）。
    - False: 如果昵称不存在。
    示例输出:
    - 如果匹配到，会打印 "{card} 在群里" 并返回 True。
    - 如果未匹配到，会打印 "{card} 不在群里" 并返回 False。
    """
    # 确保输入的 card 参数本身也不是 None，如果可能是 None，也用 "" 替换
    search_card = (card or "").lower()  # 将输入的 card 转为小写

    for member in members:
        # 获取 member.card 的值。如果 member.get("card") 返回 None，
        # 则使用空字符串 "" 作为替代，然后转换为小写。
        # (member.get("card") or "") 这一部分会处理两种情况：
        # 1. 'card' 键不存在时，get() 返回 None，然后 (None or "") 得到 ""
        # 2. 'card' 键存在但值为 None 时，get() 返回 None，然后 (None or "") 得到 ""
        # 3. 'card' 键存在且有值时，get() 返回该值，然后 (value or "") 得到 value
        member_card = (member.get("card") or "").lower()

        if search_card in member_card:  # 子字符串匹配
            print(f"{card} 在群里")  # 输出匹配信息
            return True
    # 遍历完未找到匹配
    print(f"{card} 不在群里")  # 输出未匹配信息
    return False

async def get_group_member_list(base_url=None, group_id=None, no_cache="true"):
    """
    异步调用 API 获取指定群组的成员列表，支持单个或多个 group_id，并合并结果为一个列表。

    参数:
    - base_url: API 的基础 URL，默认为 None。如果未提供，将使用 "http://127.0.0.1:3000"。
    - group_id: 群组 ID，可以是单个字符串/数字，或一个列表/数组。默认为 None，如果未提供，将打印错误并返回空列表。
    - no_cache: 是否不使用缓存，默认为 "true"（字符串）。

    返回:
    - 成员列表（数组），合并了所有指定群组的成员。如果 API 调用失败，返回空列表或部分结果。

    示例调用:
    - 单个 group_id: members = await get_group_member_list(group_id="978880814")
    - 多个 group_id: members = await get_group_member_list(group_id=["978880814", "976420087"])
    """
    if base_url is None:
        base_url = "http://127.0.0.1:3000"  # 默认 API 地址
    if group_id is None:
        print("请提供群号")
        return []

    # 检查 group_id 类型，如果是单个值，转换为列表以统一处理
    if not isinstance(group_id, (list, tuple)):
        group_id_list = [group_id]  # 转换为列表
    else:
        group_id_list = group_id  # 已经是列表

    all_members = []  # 用于存储所有成员列表

    async with aiohttp.ClientSession() as session:
        tasks = []
        for gid in group_id_list:
            # 直接在循环中定义并添加异步任务
            async def fetch_task(gid=gid):  # 使用默认参数避免闭包问题
                api_url = f"{base_url}/get_group_member_list"
                payload = {
                    "group_id": gid,
                    "no_cache": no_cache
                }
                async with session.post(api_url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("status") == "ok":
                            return data.get("data", [])  # 返回成员列表
                        else:
                            print(f"API 返回错误 for group_id {gid}: {data.get('message', '未知错误')}")
                            return []
                    else:
                        print(f"请求失败 for group_id {gid}，状态码: {response.status}")
                        return []

            tasks.append(fetch_task())  # 添加任务

        results = await asyncio.gather(*tasks, return_exceptions=True)  # 并发执行任务，捕获异常

        for result in results:
            if isinstance(result, Exception):
                print(f"API 调用异常: {str(result)}")
            elif result is not None:
                all_members.extend(result)  # 合并成员列表

    return all_members


@register_command("bc")
async def cmd_bc(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
    try:
        args_parts = args.strip().split(maxsplit=1)
        if len(args_parts) < 1:
            await send_group_msg(group_id, at_someone=user_id,
                                 text="命令格式错误，请提供用户名和踢出原因，例如: bc username")
            return
        username = args_parts[0].strip()
        # 获取所有活跃群组的 ID 列表
        active_groups = NIUNIUBOT_CONFIG.get("active_groups", [])
        if not active_groups:
            await send_group_msg(group_id, text="群号读取异常，请联系管理员")
            return

        # 获取所有活跃群组的成员列表（合并）
        members = await get_group_member_list(group_id=active_groups)
        if not members:
            await send_group_msg(group_id, text="群成员信息读取异常，请联系管理员")
            return
        check = await check_card_in_members(card=username,members=members)
        current_server_group_name = NIUNIUBOT_CONFIG.get("server_group_name", "2636")
        if not check:
            if not is_admin:
                await send_group_msg(group_id, at_someone="2720216977", text=f"非管理员身份，只进行检测，不自动踢出\n等待管理员核对并执行以下指令\ntb {username} 超杀未进群")
                return True
            await send_group_msg(group_id=group_id, text=f"/tb {current_server_group_name} {username} 超杀未进群",
                                 at_someone=3889013937)
            return True
        else:
            await send_group_msg(group_id=group_id, text=f"{username} 已进群",
                                 at_someone=user_id)

    except Exception as e:
        logger.error(f"cmd_bc 执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id, text="bc命令执行失败")

# kick, ban, tb, ub, lb commands
# ... (These commands remain largely the same, ensure they use await send_group_msg/send_private_msg) ...
# Make sure they correctly check is_admin and access NIUNIUBOT_CONFIG

async def check_play_name(group_id, user_id, player_name, server_alias):
    """
    检查玩家是否在指定或任何已配置的服务器中在线。
    如果指定了 server_alias，则只检查那个服务器；否则，遍历所有服务器。
    一旦找到玩家（精确或模糊匹配），立即返回。
    Args:
        group_id (str): QQ群ID。
        user_id (str): QQ用户ID，用于@。
        player_name (str): 要查找的玩家名称。
        server_alias (str, optional): 服务器别名，如 's1'。如果指定，则只检查该服务器。
    Returns:
        dict: 包含查找结果的字典，例如：
            {"list": "...", "is_in_game": True/False, "fix": True/False, "right_name": "..."}
    """
    config = NIUNIUBOT_CONFIG
    our_server_config = config.get("our_server", {})
    if not our_server_config:
        await send_group_msg(group_id, at_someone=user_id, text="❌ 机器人未配置任何游戏服务器信息，无法查询。")
        return {"list": "", "is_in_game": False, "fix": False, "right_name": player_name}
    lower_player_name = player_name.lower()
    if server_alias is not None:
        # 如果指定了服务器别名，只检查该服务器
        server_id = our_server_config.get(server_alias)
        if server_id is None:
            await send_group_msg(group_id, at_someone=user_id, text=f"❌ 服务器别名 '{server_alias}' 未配置。")
            return {"list": "", "is_in_game": False, "fix": False, "right_name": player_name}
        logger.info(f"正在检查指定服务器：{server_alias} (ID: {server_id})")
        # 验证服务器配置 (修改了这一行!)
        is_valid_server, validation_error_msg = validate_server_config(server_alias, our_server_config) # 传入 server_alias
        if not is_valid_server:
            logger.warning(f"服务器 '{server_alias}' (ID: {server_id}) 配置无效: {validation_error_msg}")
            await send_group_msg(group_id, at_someone=user_id, text=f"❌ 服务器 '{server_alias}' 配置无效。")
            return {"list": "", "is_in_game": False, "fix": False, "right_name": player_name}
        # 获取玩家列表
        success, player_list_str, get_players_error_msg = await get_server_players(
            server_alias, our_server_config, async_get_players_gametools
        )
        if not success:
            logger.warning(f"无法从服务器 '{server_alias}' 获取玩家列表: {get_players_error_msg}")
            await send_group_msg(group_id, at_someone=user_id, text=f"❌ 无法从服务器 '{server_alias}' 获取玩家列表。")
            return {"list": "", "is_in_game": False, "fix": False, "right_name": player_name}
        # 解析玩家列表字符串
        raw_players = re.split(r'[,\s\n\r]+', player_list_str)
        player_list = [p.strip() for p in raw_players if p.strip()]
        # 尝试精确匹配 (不区分大小写)
        for p_in_list in player_list:
            if p_in_list.lower() == lower_player_name:
                logger.info(f"玩家 '{player_name}' 在服务器 '{server_alias}' 中精确匹配成功。")
                return {"list": player_list_str, "is_in_game": True, "fix": False, "right_name": p_in_list} # 使用p_in_list确保返回的是原版名字
        # 尝试开头匹配 (不区分大小写)
        # 例如：输入 "fybielu"，正确名字 "fybieluuuuuuuu"
        for p_in_list in player_list:
            if p_in_list.lower().startswith(lower_player_name):
                logger.info(f"玩家 '{player_name}' 在服务器 '{server_alias}' 中开头匹配成功到 '{p_in_list}'。")
                return {"list": player_list_str, "is_in_game": True, "fix": True, "right_name": p_in_list}
        # 尝试模糊匹配 (相似度 > 0.75)
        best_match_name = None
        max_similarity = 0.0
        for p_in_list in player_list:
            similarity = difflib.SequenceMatcher(None, lower_player_name, p_in_list.lower()).ratio()
            if similarity > max_similarity:
                max_similarity = similarity
                best_match_name = p_in_list  # 记录原版正确名字
        if best_match_name and max_similarity > 0.75:
            logger.info(
                f"玩家 '{player_name}' 在服务器 '{server_alias}' 中模糊匹配到 '{best_match_name}' (相似度: {max_similarity:.2f})。")
            return {"list": player_list_str, "is_in_game": True, "fix": True, "right_name": best_match_name}
        # 未找到玩家
        logger.info(f"玩家 '{player_name}' 在服务器 '{server_alias}' 中未找到。")
        return {"list": player_list_str, "is_in_game": False, "fix": False, "right_name": player_name}

async def send_check_name_result(cmd,name_check_result,group_id,user_id,username,reason):
    # 检查名字是否被纠正 (fix 为 True)
    if name_check_result.get("fix", False):
        corrected_username = name_check_result.get("right_name", username)  # 获取纠正后的名字
        await send_group_msg(
            group_id=group_id,
            text=f"注意：您输入的玩家名 '{username}' 可能是 '{corrected_username}'。\n"
                 f"如果您想踢出 '{corrected_username}'，请发送正确的命令：\n"
                 f"{cmd} {corrected_username} {reason}",
            at_someone=user_id
        )
        logger.info(f"检测到玩家名 '{username}' 被纠正为 '{corrected_username}'，已提示用户。")
        return True
    elif not name_check_result.get("is_in_game", False):
        # 如果玩家名既没有找到，也没有被纠正，可以提示玩家不在线
        await send_group_msg(
            group_id=group_id,
            text=f"提示：玩家 '{username}' 当前可能已被踢出。请确认玩家是否在服务器中。",
            at_someone=user_id
        )
        logger.info(f"玩家 '{username}' 未在服务器中找到，也无相似匹配。")
        return False

@register_command("kick")
async def cmd_kick(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
    try:
        if not is_admin:
            await send_group_msg(group_id, at_someone=user_id, text="警告: 你没有权限，只有群主或管理员可以执行该命令。")
            return

        # 修改参数解析，允许最多三个部分：username, reason, [server_alias]
        args_parts = args.strip().split(maxsplit=2)
        if len(args_parts) < 2:
            await send_group_msg(group_id, at_someone=user_id,
                                 text="命令格式错误，请提供用户名和踢出原因，例如: kick username reason [server]")
            return

        username = args_parts[0].strip()
        reason = args_parts[1].strip()
        server_alias = None
        if len(args_parts) == 3:
            server_alias = args_parts[2].strip()  # 第三个参数作为服务器别名

        # 发送 kick 命令到游戏内机器人，假设命令格式支持服务器参数
        # 如果指定了服务器别名，修改命令为 /kick server_alias username reason；否则，使用原格式
        kick_command = f"/kick {username} {reason}"
        await send_group_msg(group_id=group_id, text=kick_command, at_someone=3889013937)  # 3889013937 假设是目标机器人ID
        logger.info(f"已尝试向游戏内机器人发送踢出命令：{kick_command}")

        # 调用 check_play_name，传递可选的 server_alias
        if server_alias:
            name_check_result = await check_play_name(group_id, user_id, username, server_alias=server_alias)
            cmd = "kick"
            await send_check_name_result(cmd,name_check_result, group_id, user_id, username, reason)

    except Exception as e:
        logger.error(f"执行 kick 命令时出错: {str(e)}")
        await send_group_msg(group_id, at_someone=user_id, text="❌ 命令执行失败，请检查参数或联系管理员。")

@register_command("ban")
async def cmd_ban(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):

    try:
        if not is_admin:
            await send_group_msg(group_id, at_someone=user_id, text="警告: 你没有权限，只有群主或管理员可以执行该命令。")
            return
        args_parts = args.strip().split(maxsplit=1)
        if len(args_parts) < 2:
            await send_group_msg(group_id, at_someone=user_id, text="命令格式错误，请提供用户名和ban原因，例如: ban username reason")
            return
        username = args_parts[0].strip()
        reason = args_parts[1].strip()
        current_server_group_name = NIUNIUBOT_CONFIG.get("server_group_name", "默认群组名")
        # 假设3889013937是目标机器人ID
        await send_group_msg(group_id=group_id, text=f"/ban {current_server_group_name} {username} {reason}", at_someone=3889013937)
        ban_group = NIUNIUBOT_CONFIG.get("nfj_group","")
        if ban_group:
            await send_group_msg(group_id=ban_group, text=f"{user_id} ban 了 {username} 因为 {reason}")
    except Exception as e:
        logger.error(f"cmd_ban 执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id, text="ban命令执行失败")

@register_command("tb")
async def cmd_tb(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):

    try:
        if not is_admin:
            await send_group_msg(group_id, at_someone=user_id, text="警告: 你没有权限，只有群主或管理员可以执行该命令。")
            return
        args_parts = args.strip().split(maxsplit=1)
        if len(args_parts) < 2:
            await send_group_msg(group_id, at_someone=user_id, text="命令格式错误，请提供用户名和ban原因，例如: tb username reason")
            return
        username = args_parts[0].strip()
        reason = args_parts[1].strip()
        current_server_group_name = NIUNIUBOT_CONFIG.get("server_group_name", "默认群组名")
        # 假设3889013937是目标机器人ID
        await send_group_msg(group_id=group_id, text=f"/tb {current_server_group_name} {username} {reason}", at_someone=3889013937)
    except Exception as e:
        logger.error(f"cmd_tb 执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id, text="tb命令执行失败")

@register_command("ub")
async def cmd_ub(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):

    try:
        if not is_admin:
            await send_group_msg(group_id, at_someone=user_id, text="警告: 你没有权限，只有群主或管理员可以执行该命令。")
            return
        if not args:
            await send_group_msg(group_id, at_someone=user_id, text="命令格式错误，请提供用户名，例如: ub username")
            return
        username = args.strip()
        current_server_group_name = NIUNIUBOT_CONFIG.get("server_group_name", "默认群组名")
        # 假设3889013937是目标机器人ID
        await send_group_msg(group_id=group_id, text=f"/unban {current_server_group_name} {username}", at_someone=3889013937)
    except Exception as e:
        logger.error(f"cmd_ub 执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id, text="ub命令执行失败")

@register_command("lb")
async def cmd_lb(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):

    try:
        card = "" # 初始化card
        try:
            # 使用全局循环进行等待
            bot_data = await asyncio.wait_for(get_group_member_info(group_id=group_id, user_id=user_id), timeout=5)
            card = bot_data.get('card', '').strip() # 获取card并去除空白字符
        except asyncio.TimeoutError:
            logger.error("cmd_lb: get_group_member_info 调用超时，昵称设置为默认空值")
            card = ""
        except Exception as e:
            logger.error(f"cmd_lb: 获取用户昵称失败: {str(e)}，昵称设置为默认空值", exc_info=True)
            card = ""

        username = args.strip()
        if not username:
            if not card:
                await send_group_msg(group_id, at_someone=user_id, text="未提供查询的玩家id，也无法获取你的群内昵称，命令停止执行")
                return
            await send_group_msg(group_id, at_someone=user_id, text="未提供查询的玩家id，将使用你的群内昵称作为id查询")
            username_to_use = card
        else:
            username_to_use = username

        # 假设3889013937是目标机器人ID
        await send_group_msg(group_id=group_id, text=f"/listban {username_to_use}", at_someone=3889013937)

    except Exception as e:
        logger.error(f"cmd_lb 执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id, text="lb命令执行失败")

###############################################################################
#                                 暖服机相关                                   #
###############################################################################

is_nfj_using = False
@register_command("nfj")
async def cmd_nfj(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):

    try:
        global is_nfj_using
        if not args:
            await send_group_msg(group_id, at_someone=user_id, text="请提供要叫暖服机的服务器序号")
            return

        config = NIUNIUBOT_CONFIG # Access config
        args_lower = args.lower().strip()

        nfj_server_config = config.get("nfj_server", {})
        if not isinstance(nfj_server_config, dict):
            logger.error("nfj_server 配置无效或缺失，请检查config.json")
            await send_group_msg(group_id, at_someone=user_id, text="系统错误: 暖服机服务器配置无效，请联系管理员。")
            return

        if args_lower not in nfj_server_config:
            available_servers = ', '.join(nfj_server_config.keys())
            error_message = f"请提供正确的服务器序号，当前可呼叫暖服机的服务器有：{available_servers}"
            await send_group_msg(group_id, at_someone=user_id, text=error_message)
            return

        if is_nfj_using:
            await send_group_msg(group_id, at_someone=user_id, text="暖服机指令正在转发，请稍等一会再叫暖服机")
            return

        is_nfj_using = True

        nfj_group_id = config.get("nfj_group")
        nfj_bot = config.get("nfj_bot") # nfj_bot在此命令中似乎未使用

        if not nfj_group_id: # 只检查nfj_group_id，因为它用于发送
             logger.error("nfj_group 配置缺失或无效，请检查config.json")
             await send_group_msg(group_id, at_someone=user_id, text="系统错误: 暖服机群组配置无效，请联系管理员。")
             return

        if not config.get("nfj_on", False): # 直接检查布尔值
            await send_group_msg(group_id, at_someone=user_id, text="暖服机指令未启用，正在呼叫管理员为你叫暖服机")
            admin_qq = config.get("admin_qq") # 假设admin_qq是手动呼叫的联系人
            if admin_qq:
                 # 通过私聊或群消息向管理员发送提醒
                 await send_private_msg(user_id=admin_qq, text=f"有群成员需要暖服机: 服务器序号 {args_lower}, 来自群组 {group_id}, 用户 {user_id}")
                 await send_group_msg(group_id=group_id, text="已通知管理员。", at_someone=user_id) # 向用户确认
            else:
                 await send_group_msg(group_id, at_someone=user_id, text="未配置管理员QQ，无法通知管理员。请联系群管理员。")
            return # 如果nfj关闭则在此停止


        current_server_group_name = config.get("server_group_name", "默认群组名")
        send_msg_id = await send_group_msg(group_id=nfj_group_id, text=f"/warm {current_server_group_name} {args_lower}")

        if send_msg_id is None:
             logger.error(f"发送 /warm 命令到群组 {nfj_group_id} 失败。")
             await send_group_msg(group_id, at_someone=user_id, text="发送暖服机命令失败，请稍后重试。")
             return

        # 使用正确的group_id（期望响应的地方），即nfj_group_id
        def nfj_match(msg):
            raw_message_lower = msg.get('raw_message', '').lower()
            # 如果消息中包含server_group_name和args，或者包含'队列'则匹配
            # 还要考虑响应可能来自nfj_bot本身（提及它）
            bot_qq = config.get("nfj_bot") # 获取nfj_bot QQ
            bot_mention = f'[cq:at,qq={bot_qq}]' if bot_qq else ''
            return (current_server_group_name.lower() in raw_message_lower and args_lower in raw_message_lower) or \
                   ('队列' in raw_message_lower) or \
                   (bot_mention and bot_mention in raw_message_lower) # 匹配@nfj_bot

        rsp_data = await check_rsp(msg_id=send_msg_id, group_id=nfj_group_id, match_condition=nfj_match)

        # 如果check_rsp失败或超时，它返回错误消息数组
        # 将响应数据发送回原始群组
        await send_group_msg(group_id, at_someone=user_id, json_data=rsp_data)
        logger.info(f"已成功转发 /warm {current_server_group_name} {args_lower} 命令结果到群组 {group_id}")

    except Exception as e:
        logger.error(f"cmd_nfj 命令执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id, text="命令执行失败，请联系管理员。")
    finally:
        is_nfj_using = False # Reset the flag


is_nfb_using = False
@register_command("nfb")
async def cmd_nfb(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):

    try:
        global is_nfb_using
        if is_nfb_using:
            await send_group_msg(group_id, at_someone=user_id, text="暖服机指令正在转发，请稍等一会再查询暖服机状态")
            return

        is_nfb_using = True
        config = NIUNIUBOT_CONFIG

        nfj_group_id = config.get("nfj_group")
        # nfj_bot = config.get("nfj_bot") # nfj_bot似乎未使用

        if not nfj_group_id:
            logger.error("nfj_group 配置缺失或无效，请检查config.json")
            await send_group_msg(group_id, at_someone=user_id, text="系统错误: 暖服机群组配置无效，请联系管理员。")
            return

        if not config.get("nfj_on", False):
            await send_group_msg(group_id, at_someone=user_id, text="暖服机查询指令未启用")
            return

        # 向特定群组发送/nfb命令
        send_msg_id = await send_group_msg(group_id=nfj_group_id, text=f"/nfb")

        if send_msg_id is None:
             logger.error(f"发送 /nfb 命令到群组 {nfj_group_id} 失败。")
             await send_group_msg(group_id, at_someone=user_id, text="发送暖服机查询命令失败，请稍后重试。")
             return

        # 使用正确的group_id检查响应
        def nfb_match(msg):
            raw_message_lower = msg.get('raw_message', '')
            # 如果消息中包含任何关键词则匹配
            return '暖服' in raw_message_lower or '等待' in raw_message_lower

        rsp_data = await check_rsp(msg_id=send_msg_id, group_id=nfj_group_id, match_condition=nfb_match)

        # 将响应数据发送回原始群组
        await send_group_msg(group_id, at_someone=user_id, json_data=rsp_data)
        logger.info(f"已成功转发 /nfb 命令结果到群组 {group_id}")

    except Exception as e:
        logger.error(f"cmd_nfb 命令执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id, text="命令执行失败，请联系管理员。")
    finally:
        is_nfb_using = False # Reset the flag


# AI判断相关的辅助函数
async def ai_judgment(image_url, from_who, player_list_str=None):
    """AI判断函数"""
    if not image_url or not isinstance(image_url, str):
        logger.error("无效的image_url参数")
        return "判断失败: 无效的图像URL"
    # user_id 可以是 int 或 str
    if not isinstance(from_who, (int, str)):
        logger.error("无效的from_who参数")
        return "判断失败: 无效的用户ID"

    ai_judgment_url = NIUNIUBOT_CONFIG.get("ai_judgement", "")
    if not ai_judgment_url:
        logger.error("AI判断URL配置缺失，请检查config.json中的'ai_judgement'")
        return "判断失败: AI配置不完整"

    payload = {
        "image_url": image_url,
        "from_who": str(from_who), # 转换为字符串以保持载荷一致性
    }
    if player_list_str is not None and player_list_str.strip() != "": # 检查是否为非空字符串
        payload["player_list_str"] = player_list_str.strip() # 使用去除空格的字符串

    max_retries = 5
    backoff_factor = 2
    headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"}

    for attempt in range(max_retries):
        try:
            async with aiohttp.ClientSession() as session:
                # 如果需要，调整AI判断的超时时间，60秒可能太短/太长
                timeout = aiohttp.ClientTimeout(total=90) # 示例：90秒超时
                async with session.post(
                        ai_judgment_url,
                        json=payload,
                        headers=headers,
                        timeout=timeout
                ) as response:
                    logger.info(f"AI判断响应状态码: {response.status}")
                    response.raise_for_status()
                    result_json = await response.json()

                    # 检查AI服务预期的特定结构
                    if isinstance(result_json, dict) and "error" in result_json:
                         # 假设'error'键表示AI端的失败
                         return f"判断失败: {result_json.get('error', '未知错误')}"
                    elif isinstance(result_json, dict) and "judgment" in result_json:
                         # 假设'judgment'键表示成功
                         return result_json # 返回字典结果
                    else:
                         # 意外的成功响应格式
                         logger.error(f"AI 判断API返回未知格式: {result_json}")
                         return "判断失败: AI服务返回未知格式"

        except aiohttp.ClientError as e:
            if attempt == max_retries - 1:
                logger.error(f"AI 判断失败 (尝试 {max_retries} 次): {str(e)}")
                return f"判断失败: {str(e)}"
            logger.warning(f"AI请求错误: {str(e)}，尝试 {attempt + 1}/{max_retries}")
            await asyncio.sleep(backoff_factor ** attempt)

        except asyncio.TimeoutError:
            if attempt == max_retries - 1:
                logger.error(f"AI 判断超时 (尝试 {max_retries} 次)")
                return "判断失败: AI服务响应超时"
            logger.warning(f"AI请求超时，尝试 {attempt + 1}/{max_retries}")
            await asyncio.sleep(backoff_factor ** attempt)

        except Exception as e:
            if attempt == max_retries - 1:
                logger.error(f"AI 判断异常 (尝试 {max_retries} 次): {str(e)}")
                return f"判断失败: {str(e)}"
            logger.warning(f"AI请求异常: {str(e)}，尝试 {attempt + 1}/{max_retries}")
            await asyncio.sleep(backoff_factor ** attempt)

    return "判断失败: AI服务多次请求失败，请稍后再试"

# 1. 分离确认在游戏中的玩家和不在游戏中的玩家
def separate_players(player_string):
    """
    分离玩家为在游戏中和不在游戏中两部分

    Args:
        player_string (str): 包含玩家信息的字符串，可能包含"确认在游戏中"前缀

    Returns:
        tuple: (confirmed_players, unconfirmed_players) 两个列表
    """
    confirmed_players = []
    unconfirmed_players = []

    # 按逗号分割并处理每个玩家
    players_raw = [p.strip() for p in player_string.split(',') if p.strip()]

    for player_raw in players_raw:
        if player_raw.startswith("确认在游戏中"):
            # 去除前缀并获取实际玩家名称
            actual_name = player_raw.replace("确认在游戏中", "").strip()
            if actual_name:
                confirmed_players.append(actual_name)
                logger.info(f"玩家{actual_name}确认在游戏中")
        else:
            # 玩家未确认在游戏中
            if player_raw:
                unconfirmed_players.append(player_raw)
                logger.info(f"玩家{player_raw}未确认在游戏中")

    return confirmed_players, unconfirmed_players


# 2. 获取服务器玩家列表
async def get_server_players(server_key, server_config, async_get_players_gametools):
    """
    获取指定服务器的在线玩家列表

    Args:
        server_key (str): 服务器标识
        server_config (dict): 服务器配置字典
        async_get_players_gametools: 异步获取玩家数据的函数

    Returns:
        tuple: (success, player_list_str, error_msg)
        - success (bool): 是否成功获取
        - player_list_str (str): 玩家名称列表，逗号分隔
        - error_msg (str): 错误信息（如果有）
    """
    try:
        if server_key not in server_config:
            available_servers = ', '.join(server_config.keys()) if server_config else "无"
            return False, "", f"无效的服务器号。当前可用服务器: {available_servers}"

        game_id = server_config[server_key]

        # 异步获取玩家数据
        players_data = await async_get_players_gametools(game_id=game_id)

        if players_data and 'teams' in players_data:
            player_names = []
            for team in players_data.get("teams", []):
                for player in team.get("players", []):
                    player_name = player.get("name", "").strip()
                    if player_name:  # 仅添加非空名称
                        player_names.append(player_name)

            player_list_str = ", ".join(player_names)
            return True, player_list_str, ""
        else:
            logger.warning(f"无法获取服务器 {server_key} 的玩家数据")
            return False, "", f"无法获取服务器 {server_key} 的当前玩家列表"

    except Exception as e:
        logger.error(f"获取服务器 {server_key} 玩家列表失败: {str(e)}", exc_info=True)
        return False, "", f"获取玩家列表时发生错误: {str(e)}"


# 3. 从消息中提取图片URL
def extract_image_url_from_message(message_array):
    """
    从消息数组中提取图片URL

    Args:
        message_array (list): 消息数组，包含各种消息类型

    Returns:
        str: 图片URL，如果没有找到则返回None
    """
    for item in message_array:
        if item.get("type") == "image":
            url = item.get("data", {}).get("url")
            if url:
                return url
    return None


# 4. 获取消息中的图片URL（支持回复消息）
async def get_image_from_message(message_data, get_message_detail, config, timeout=15):
    """
    从消息中获取图片URL，支持从回复的消息中获取

    Args:
        message_data (dict): 消息数据
        get_message_detail: 获取消息详情的函数
        config: 配置对象
        timeout (int): 超时时间（秒）

    Returns:
        tuple: (success, image_url, error_msg)
        - success (bool): 是否成功获取图片
        - image_url (str): 图片URL
        - error_msg (str): 错误信息（如果有）
    """
    try:
        message_array = message_data.get("message", [])
        reply_id = None
        image_url = None

        # 检查当前消息中的回复和图片
        for item in message_array:
            if item.get("type") == "reply":
                reply_id = item["data"].get("id")
            elif item.get("type") == "image":
                url = item.get("data", {}).get("url")
                if url:
                    image_url = url

        # 如果有回复，优先从被回复消息中获取图片
        if reply_id:
            logger.info(f"检测到回复消息，目标消息ID: {reply_id}")
            try:
                message_detail = await asyncio.wait_for(
                    get_message_detail(message_id=reply_id, config=config),
                    timeout=timeout
                )
                detail_message_array = message_detail.get("message", [])
                image_url_from_reply = extract_image_url_from_message(detail_message_array)

                if image_url_from_reply:
                    image_url = image_url_from_reply
                elif not image_url:
                    return False, None, "被回复的消息中未找到图片，当前消息中也没有图片"

            except asyncio.TimeoutError:
                logger.error(f"获取被回复消息详情超时 (msg_id {reply_id})")
                if not image_url:
                    return False, None, "获取被回复消息详情超时，且当前消息无图片"
                else:
                    logger.warning("获取被回复消息详情超时，但当前消息包含图片，将使用当前图片")

            except Exception as e:
                logger.error(f"获取被回复消息详情失败 (msg_id {reply_id}): {str(e)}", exc_info=True)
                if not image_url:
                    return False, None, f"获取被回复的消息详情失败，且当前消息无图片。错误: {e}"
                else:
                    logger.warning("获取被回复消息详情失败，但当前消息包含图片，将使用当前图片")

        # 最终检查是否有图片URL
        if not image_url:
            return False, None, "请提供证据（单张图片），或回复包含图片的消息"

        return True, image_url, ""

    except Exception as e:
        logger.error(f"获取消息图片失败: {str(e)}", exc_info=True)
        return False, None, f"获取消息图片时发生错误: {str(e)}"


# 5. 构建ban命令
def build_ban_commands(player_string, reason="语言攻击"):
    """
    根据玩家字符串构建ban命令

    Args:
        player_string (str): 包含玩家信息的字符串
        reason (str): ban的原因

    Returns:
        tuple: (ban_commands_list, player_status_text)
        - ban_commands_list (list): ban命令列表
        - player_status_text (str): 玩家状态描述文本
    """
    confirmed_attackers, unconfirmed_attackers = separate_players(player_string)

    if not confirmed_attackers and not unconfirmed_attackers:
        return [], "未能解析到有效的玩家名字"

    # 构建ban命令
    ban_commands = []
    if confirmed_attackers:
        ban_commands.append("# 已确认在游戏中的玩家:")
        for p in confirmed_attackers:
            ban_commands.append(f"ban {p} {reason}")

    if unconfirmed_attackers:
        ban_commands.append("# 玩家不在游戏中，请注意检测:")
        for p in unconfirmed_attackers:
            ban_commands.append(f"ban {p} {reason}")

    # 构建玩家状态信息
    player_status = []
    if confirmed_attackers:
        player_status.append(f"已确认在游戏中: {', '.join(confirmed_attackers)}")
    if unconfirmed_attackers:
        player_status.append(f"不在游戏中: {', '.join(unconfirmed_attackers)}")

    player_status_text = "; ".join(player_status)

    return ban_commands, player_status_text


# 6. 验证服务器配置
def validate_server_config(server_key, server_config):
    """
    验证服务器配置是否有效

    Args:
        server_key (str): 服务器标识
        server_config (dict): 服务器配置

    Returns:
        tuple: (is_valid, error_msg)
        - is_valid (bool): 配置是否有效
        - error_msg (str): 错误信息（如果配置无效）
    """
    if not isinstance(server_config, dict):
        return False, "服务器配置格式错误"

    if server_key not in server_config:
        available_servers = ', '.join(server_config.keys()) if server_config else "无"
        return False, f"无效的服务器号。当前可用服务器: {available_servers}"

    return True, ""


# 7. 处理AI判断结果
async def process_ai_judgment_result(judgment_result, server_key, user_id, group_id,
                                     send_group_msg, admin_qq, logger):
    """
    处理AI判断结果并发送相应消息

    Args:
        judgment_result: AI判断结果
        server_key (str): 服务器标识
        user_id (str): 用户ID
        group_id (str): 群组ID
        send_group_msg: 发送群消息的函数
        admin_qq (str): 管理员QQ号
        logger: 日志记录器

    Returns:
        bool: 是否成功处理
    """
    try:
        if isinstance(judgment_result, dict):
            judgment = judgment_result.get("judgment")
            player = judgment_result.get("player", "未提取到玩家")
            chat = judgment_result.get("chat", "未提取到对话内容")
            all_player = judgment_result.get("all_player", "未提取到玩家")

            logger.debug(f"AI原始结果: {judgment_result}")

            if judgment == "1":  # 检测到语言攻击
                logger.info(f"AI检测到语言攻击。玩家: {player}, 对话: {chat}")

                if player == "未提取到玩家" or not player.strip():
                    await send_group_msg(group_id, at_someone=user_id,
                                         text=f"AI检测到可能有语言攻击，但未能提取到玩家信息，请联系管理员检查。对话内容:\n{chat}")
                    return False

                ban_commands, player_status_text = build_ban_commands(player)

                if not ban_commands:
                    await send_group_msg(group_id, at_someone=user_id,
                                         text=f"AI检测到可能有语言攻击，但未能解析到有效的玩家名字，请联系管理员检查。对话内容:\n{chat}")
                    return False

                if not admin_qq:
                    logger.error("check_admin 配置缺失")
                    await send_group_msg(group_id, at_someone=user_id,
                                         text="AI检测到语言攻击，但未配置管理员QQ，无法通知管理员进行处理。请联系群管理员。")
                    return False

                ban_text = "\n".join(ban_commands)

                # 发送用户消息
                await send_group_msg(group_id, at_someone=user_id,
                                     text=f"\n因AI仍存在误差,为不引发争议,正在请管理员判定\n对话内容:\n{chat}\nAI检测到玩家: {player_status_text} 有语言攻击。")

                # 通知管理员
                await send_group_msg(group_id, at_someone="2720216977",
                                     text=f"\n接到用户 {user_id} 举报的语言攻击，服务器 {server_key}。\n\n{player_status_text}\n\n请在确认后发送下面的命令:\n{ban_text}")

                logger.info(f"已通知管理员 {admin_qq} 关于语言攻击举报。")

            else:  # 未检测到语言攻击
                logger.info(f"AI未检测到语言攻击。玩家: {player}, 对话: {chat}")

                confirmed_all, unconfirmed_all = separate_players(all_player)
                all_player_status = []
                if confirmed_all:
                    all_player_status.append(f"已确认在游戏中: {', '.join(confirmed_all)}")
                if unconfirmed_all:
                    all_player_status.append(f"不在游戏中: {', '.join(unconfirmed_all)}")

                all_player_status_text = "; ".join(all_player_status) if all_player_status else "未提取到玩家"

                await send_group_msg(group_id, at_someone=user_id,
                                     text=f"\n对话内容:\n{chat}\n玩家: {all_player_status_text}\n未检测到有语言攻击，如有异议请联系管理员")

            return True

        elif isinstance(judgment_result, str) and "失败" in judgment_result:
            logger.error(f"AI判断返回失败字符串: {judgment_result}")
            await send_group_msg(group_id, at_someone=user_id,
                                 text=f"AI处理失败: {judgment_result}. 请重试或联系管理员。")
            return False

        else:
            logger.error(f"AI判断返回异常结果: {judgment_result}")
            await send_group_msg(group_id, at_someone=user_id,
                                 text="AI处理返回异常结果，请联系管理员检查。")
            return False

    except Exception as e:
        logger.error(f"处理AI判断结果失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id,
                             text="处理AI判断结果时发生错误，请联系管理员。")
        return False


# ======================= 重构后的 zc 命令 =======================

# 处理嘴臭举报命令 zc (使用提取的函数重构)
@register_command("zc")
async def cmd_zc(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
    """重构后的嘴臭举报命令，使用提取的通用函数"""
    try:
        # 1. 基础参数验证
        if message_data is None:
            await send_group_msg(group_id, at_someone=user_id, text="消息数据缺失，无法处理举报")
            return

        args_parts = args.strip().split()
        if len(args_parts) < 1:
            await send_group_msg(group_id, at_someone=user_id,
                                 text="请提供服务器号（例如 zc s1），并确保消息包含图片或回复图片消息")
            return

        # 2. 验证服务器配置
        config = NIUNIUBOT_CONFIG
        server_key = args_parts[0].lower()
        our_server_config = config.get("our_server", {})

        is_valid, error_msg = validate_server_config(server_key, our_server_config)
        if not is_valid:
            await send_group_msg(group_id, at_someone=user_id, text=error_msg)
            return

        # 3. 发送处理确认消息
        await send_group_msg(group_id, at_someone=user_id,
                             text=f"已接收到举报，正在处理服务器 {server_key} 的玩家数据...")

        # 4. 获取服务器玩家列表
        success, player_list_str, error_msg = await get_server_players(
            server_key, our_server_config, async_get_players_gametools
        )
        if not success:
            logger.warning(f"cmd_zc: {error_msg}")
            player_list_str = ""  # 继续执行，但没有玩家列表

        # 5. 获取消息中的图片
        success, image_url, error_msg = await get_image_from_message(
            message_data, get_message_detail, config
        )
        if not success:
            await send_group_msg(group_id, at_someone=user_id, text=error_msg)
            return

        # 6. 调用AI判断
        logger.info(f"cmd_zc: 正在调用AI处理图片...")
        judgment_result = await ai_judgment(
            image_url=image_url,
            from_who=user_id,
            player_list_str=player_list_str
        )
        logger.info(f"cmd_zc: AI处理结束，结果类型: {type(judgment_result)}")

        # 7. 处理AI判断结果
        admin_qq = config.get("check_admin")
        await process_ai_judgment_result(
            judgment_result, server_key, user_id, group_id,
            send_group_msg, admin_qq, logger
        )

    except Exception as e:
        logger.error(f"cmd_zc 命令执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id, text="命令执行失败，请联系管理员")


async def universal_ai_judgment(image_url, system_prompt, user_prompt, extract_rules):
    """AI判断函数"""
    if not image_url or not isinstance(image_url, str):
        logger.error("无效的image_url参数")
        return "判断失败: 无效的图像URL"

    universal_ai_judgment_url = NIUNIUBOT_CONFIG.get("universal_ai_judgment", "")
    if not universal_ai_judgment_url:
        logger.error("AI判断URL配置缺失，请检查config.json中的'universal_ai_judgment'")
        return "判断失败: AI配置不完整"

    payload = {
        "image_url": image_url,
        "system_prompt": system_prompt,
        "user_prompt": user_prompt,
        "extract_rules": extract_rules
    }

    max_retries = 5
    backoff_factor = 2
    headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"}

    for attempt in range(max_retries):
        try:
            async with aiohttp.ClientSession() as session:
                # 如果需要，调整AI判断的超时时间，60秒可能太短/太长
                timeout = aiohttp.ClientTimeout(total=90) # 示例：90秒超时
                async with session.post(
                        universal_ai_judgment_url,
                        json=payload,
                        headers=headers,
                        timeout=timeout
                ) as response:
                    logger.info(f"AI判断响应状态码: {response.status}")
                    response.raise_for_status()
                    result_json = await response.json()

                    # 检查AI服务预期的特定结构
                    if isinstance(result_json, dict) and "error" in result_json:
                         # 假设'error'键表示AI端的失败
                         return f"判断失败: {result_json.get('error', '未知错误')}"
                    return result_json # 返回字典结果

        except aiohttp.ClientError as e:
            if attempt == max_retries - 1:
                logger.error(f"AI 判断失败 (尝试 {max_retries} 次): {str(e)}")
                return f"判断失败: {str(e)}"
            logger.warning(f"AI请求错误: {str(e)}，尝试 {attempt + 1}/{max_retries}")
            await asyncio.sleep(backoff_factor ** attempt)

        except asyncio.TimeoutError:
            if attempt == max_retries - 1:
                logger.error(f"AI 判断超时 (尝试 {max_retries} 次)")
                return "判断失败: AI服务响应超时"
            logger.warning(f"AI请求超时，尝试 {attempt + 1}/{max_retries}")
            await asyncio.sleep(backoff_factor ** attempt)

        except Exception as e:
            if attempt == max_retries - 1:
                logger.error(f"AI 判断异常 (尝试 {max_retries} 次): {str(e)}")
                return f"判断失败: {str(e)}"
            logger.warning(f"AI请求异常: {str(e)}，尝试 {attempt + 1}/{max_retries}")
            await asyncio.sleep(backoff_factor ** attempt)

    return "判断失败: AI服务多次请求失败，请稍后再试"


async def process_bca_ai_judgment_result(judgment_result, server_player_list, server_key, user_id, group_id,
                                         send_group_msg, admin_qq, logger):
    """
    处理AI判断结果并发送相应消息
    Args:
        judgment_result: AI判断结果
        server_player_list (str): 逗号分隔的服务器玩家列表字符串
        server_key (str): 服务器标识
        user_id (str): 用户ID
        group_id (str): 群组ID
        send_group_msg: 发送群消息的函数
        admin_qq (str): 管理员QQ号
        logger: 日志记录器
    Returns:
        bool: 是否成功处理
    """
    try:
        if isinstance(judgment_result, dict):
            raw_ai_players = judgment_result.get("player", "未提取到玩家")
            logger.debug(f"AI原始结果: {judgment_result}")

            # 如果AI没有提取到玩家，则直接返回
            if raw_ai_players == "未提取到玩家" or not raw_ai_players.strip():
                await send_group_msg(group_id, at_someone=user_id,
                                     text=f"没有玩家超杀")
                return True

            # 1. 准备服务器玩家列表用于快速查找和保留原始大小写
            server_player_set_lower = set()
            server_player_map_original = {}  # 存储小写到原始大小写的映射
            if server_player_list:
                for p in server_player_list.split(','):
                    player_name = p.strip()
                    if player_name:
                        server_player_set_lower.add(player_name.lower())
                        server_player_map_original[player_name.lower()] = player_name

            # 2. 解析和清洗AI提取的玩家ID
            # 移除 '[]' 及其内部内容，以及 AI 可能添加的 '确认在游戏中' 前缀
            ai_player_candidates_cleaned = []
            for p in raw_ai_players.split(','):
                # 使用正则表达式移除方括号内容和 "确认在游戏中" 前缀
                cleaned_p = re.sub(r"\[.*?\]|确认在游戏中", "", p).strip()
                if cleaned_p:
                    ai_player_candidates_cleaned.append(cleaned_p)

            found_in_server_list_display = []  # 存储在服务器玩家列表中找到的玩家 (会加上“确认在游戏中”前缀)
            not_found_in_server_list_display = []  # 存储未在服务器玩家列表中找到的玩家 (不加前缀)

            # This list will hold the corrected player names that we will act upon (check group, kick)
            # It stores the *actual* player ID to use for API calls and group checks, after correction.
            players_for_action = []  # Stores actual player IDs (e.g., "IIIHAIR", "BMW_M4_It_My_Car")

            # 3. 对比并纠正玩家ID，并填充 display lists and players_for_action
            for ai_player_candidate in ai_player_candidates_cleaned:
                corrected_player_name = ai_player_candidate  # 默认使用AI原始提取的（已清洗）名称
                is_found_in_server_list = False

                # 尝试精确匹配 (忽略大小写)
                if ai_player_candidate.lower() in server_player_set_lower:
                    corrected_player_name = server_player_map_original[ai_player_candidate.lower()]  # 使用原始大小写
                    found_in_server_list_display.append(f"确认在游戏中{corrected_player_name}")
                    is_found_in_server_list = True
                else:
                    # 尝试模糊匹配 (相似度 > 0.75)
                    if server_player_set_lower:  # 只有当服务器玩家列表不为空时才进行模糊匹配
                        best_match = None
                        max_similarity = 0.0
                        for server_p_lower, server_p_original in server_player_map_original.items():
                            similarity = difflib.SequenceMatcher(None, ai_player_candidate.lower(),
                                                                 server_p_lower).ratio()
                            if similarity > max_similarity:
                                max_similarity = similarity
                                best_match = server_p_original  # 存储原始大小写的玩家名

                        if best_match and max_similarity >= 0.75:  # 确保相似度达到阈值
                            corrected_player_name = best_match
                            found_in_server_list_display.append(f"确认在游戏中{corrected_player_name}")
                            is_found_in_server_list = True

                # 如果既没有精确匹配也没有模糊匹配，则视为未找到在服务器列表
                if not is_found_in_server_list:
                    not_found_in_server_list_display.append(
                        ai_player_candidate)  # Use original candidate for display here

                # Add the corrected player name for further processing (group check, kick)
                # Ensure unique names are added for action to avoid redundant checks/kicks
                if corrected_player_name not in players_for_action:
                    players_for_action.append(corrected_player_name)

            # 4. 格式化并发送第一次结果消息 (关于服务器玩家列表匹配)
            found_str = "、".join(found_in_server_list_display) if found_in_server_list_display else "无"
            not_found_str = "、".join(not_found_in_server_list_display) if not_found_in_server_list_display else "无"
            await send_group_msg(group_id, at_someone=user_id,
                                 text=f"超杀玩家id:\n玩家列表中找到:{found_str}\n未在玩家列表中找到:{not_found_str}")

            # --- 以下是新增的群成员检测和踢出逻辑 ---

            # 5. 获取所有活跃群组的 ID 列表
            active_groups = NIUNIUBOT_CONFIG.get("active_groups", [])
            if not active_groups:
                await send_group_msg(group_id,
                                     text="警告: Bot配置中'active_groups'为空或缺失，无法检查群成员。请联系管理员添加群组ID。")
                logger.error("NIUNIUBOT_CONFIG中'active_groups'配置缺失或为空，无法执行群成员检查。")
                return False

            # 获取所有活跃群组的成员列表（合并）
            members = await get_group_member_list(base_url=NIUNIUBOT_CONFIG.get("api_base_url"), group_id=active_groups)
            if not members:
                await send_group_msg(group_id,
                                     text="警告: 获取群成员信息异常，无法执行群成员检测和踢出操作。请联系管理员。")
                logger.error("获取群成员列表失败，可能API不可用或群ID错误。")
                return False

            current_server_group_name = NIUNIUBOT_CONFIG.get("server_group_name", "2636")  # 从配置获取，默认为 "2636"
            admin_to_at = "3889013937"

            # 用于汇总踢出结果
            players_remained_in_group = []

            # 6. 逐个检测玩家是否在群里并执行踢出操作
            for player_id_to_check in players_for_action:
                check_result = await check_card_in_members(card=player_id_to_check, members=members)

                if not check_result:  # 如果玩家不在群里
                    logger.info(f"玩家 '{player_id_to_check}' 不在群里，尝试踢出...")
                    # 无论踢出是否成功，都发送 `/tb` 命令消息到群里 (假设 `/tb` 是给机器人的指令)
                    await send_group_msg(
                        group_id=group_id,
                        text=f"/tb {current_server_group_name} {player_id_to_check} 超杀未进群",
                        at_someone=admin_to_at
                    )

                else:
                    players_remained_in_group.append(player_id_to_check)
                    logger.info(f"玩家 '{player_id_to_check}' 在群里，不进行踢出操作。")


        else:
            await send_group_msg(group_id, at_someone=user_id,
                                 text=f"AI判断结果格式不正确：{judgment_result}")
            return False

    except Exception as e:
        logger.error(f"处理bca结果失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id,
                             text="处理bca结果时发生错误，请联系管理员。")
        return False

@register_command("bca")
async def cmd_bca(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
    try:
        if not is_admin:
            await send_group_msg(group_id, at_someone=user_id, text="当前仍有细节未补充，仅对管理员开放")
            return
        # 1. 基础参数验证
        if message_data is None:
            await send_group_msg(group_id, at_someone=user_id, text="消息数据缺失，无法处理举报")
            return

        args_parts = args.strip().split()
        if len(args_parts) < 1:
            await send_group_msg(group_id, at_someone=user_id,
                                 text="请提供服务器号（例如 bca s1），并确保消息包含图片或回复图片消息")
            return

        # 2. 验证服务器配置
        config = NIUNIUBOT_CONFIG
        server_key = args_parts[0].lower()
        our_server_config = config.get("our_server", {})

        is_valid, error_msg = validate_server_config(server_key, our_server_config)
        if not is_valid:
            await send_group_msg(group_id, at_someone=user_id, text=error_msg)
            return

        # 3. 发送处理确认消息
        await send_group_msg(group_id, at_someone=user_id,
                             text=f"已接收到自动踢出超杀指令，正在处理服务器 {server_key} 的玩家数据...")

        # 4. 获取服务器玩家列表
        success, player_list_str, error_msg = await get_server_players(
            server_key, our_server_config, async_get_players_gametools
        )
        if not success:
            logger.warning(f"cmd_zc: {error_msg}")
            player_list_str = ""  # 继续执行，但没有玩家列表

        # 5. 获取消息中的图片
        success, image_url, error_msg = await get_image_from_message(
            message_data, get_message_detail, config
        )
        if not success:
            await send_group_msg(group_id, at_someone=user_id, text=error_msg)
            return

        system_prompt = "You are a Server Administrator of the Battlefield Five community, and you need to deal with reports from group members fairly."
        user_content_text = f"""
        这是我给你提供的当前服务器的玩家名单:{player_list_str if player_list_str else ""}\n
        参照玩家名单来帮助你核对你通过图片提取的用户名，如果我给你的玩家名单为空，或者和你自己提取的玩家用户名几乎完全没有相近的，那就直接使用你自己提取出来的用户名。
        请分析图片中的战地五得分板K列对应击杀，有些玩家加入了战排他们的id前面会有‘[战排名字]’注意去除'['']'以及他们之间的内容
        任务步骤：
        1. 检测图片中击杀超过80的玩家id
        结果请按以下格式输出：
        <player>
        [在此处列出击杀超过80的玩家，多个玩家用英文逗号分隔，如果玩家的id在提供的玩家名单里则在他的id前加上“确认在游戏中”，不在就不加]
        </player>
        """
        extract_rules = json.dumps({
            "player": "<player>(.*?)</player>"
        })

        # 6. 调用AI判断
        logger.info(f"cmd_zc: 正在调用AI处理图片...")
        judgment_result = await universal_ai_judgment(
            image_url=image_url,
            system_prompt= system_prompt,
            user_prompt= user_content_text,
            extract_rules= extract_rules
        )
        logger.info(f"cmd_zc: AI处理结束，结果类型: {type(judgment_result)}")

        # 7. 处理AI判断结果
        admin_qq = config.get("check_admin")
        await process_bca_ai_judgment_result(
            judgment_result, player_list_str,server_key, user_id, group_id,
            send_group_msg, admin_qq, logger
        )

    except Exception as e:
        logger.error(f"cmd_bca 命令执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id, text="bca命令执行失败，请联系管理员")

@register_command("snf")
async def cmd_snf(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
    try:
        # 异步读取机器人名单文件
        bot_names_set = await load_bot_names_async()
        if not bot_names_set:
            error_msg = "错误: 机器人名单文件读取失败或为空。请检查文件 nfj.txt。"
            logger.warning(error_msg)
            await send_group_msg(group_id, at_someone=user_id, text=error_msg)
            return

        # 解析参数
        args_parts = args.strip().split()
        if len(args_parts) < 1:
            await send_group_msg(group_id, at_someone=user_id, text="请提供服务器号（例如 snf s1）")
            return

        server_key = args_parts[0].lower()

        # 验证服务器配置
        our_server_config = NIUNIUBOT_CONFIG.get("our_server", {})
        if not isinstance(our_server_config, dict) or server_key not in our_server_config:
            available_servers = ', '.join(our_server_config.keys()) if our_server_config else "无"
            await send_group_msg(group_id, at_someone=user_id,
                                 text=f"无效的服务器号。当前可用服务器: {available_servers}")
            return

        game_id = our_server_config[server_key]

        # 使用超时控制获取玩家数据
        try:
            players_data = await asyncio.wait_for(
                async_get_players_gametools(game_id=game_id),
                timeout=10.0  # 10秒超时
            )
        except asyncio.TimeoutError:
            await send_group_msg(group_id, at_someone=user_id,
                                 text="获取玩家数据超时，请稍后重试。")
            return
        except Exception as e:
            logger.error(f"获取玩家数据失败: {str(e)}")
            await send_group_msg(group_id, at_someone=user_id,
                                 text="无法获取玩家数据，请稍后重试。")
            return

        # 验证玩家数据结构
        if not players_data or 'teams' not in players_data:
            await send_group_msg(group_id, at_someone=user_id, text="玩家数据格式异常")
            return

        teams = players_data['teams']
        if len(teams) != 2:
            await send_group_msg(group_id, at_someone=user_id, text="玩家数据不完整")
            return

        # 查找团队数据
        attacker_team_data = None  # 美军 (USA_Pacific)
        defender_team_data = None  # 日军 (JPN)

        for team in teams:
            if team['key'] == 'USA_Pacific':
                attacker_team_data = team
            elif team['key'] == 'JPN':
                defender_team_data = team

        if not attacker_team_data or not defender_team_data:
            await send_group_msg(group_id, at_someone=user_id,
                                 text="无法找到有效的团队数据（USA_Pacific 或 JPN）。")
            return

        # 计算统计数据
        defender_real, defender_bot = count_real_and_bot(defender_team_data, bot_names_set)
        attacker_real, attacker_bot = count_real_and_bot(attacker_team_data, bot_names_set)

        # 判断暖服状态
        nuanfu_status = "暖服结束" if defender_real >= 25 else "正在暖服"

        # 构建并发送结果
        summary_text = (f"\n当前 {server_key} 暖服状态: {nuanfu_status}\n"
                        f"日军: 真人 {defender_real}, 暖服机 {defender_bot} \n"
                        f"美军: 真人 {attacker_real}, 暖服机 {attacker_bot} \n")

        await send_group_msg(group_id, at_someone=user_id, text=summary_text)

    except Exception as e:
        logger.error(f"snf 命令执行失败: {str(e)}", exc_info=True)
        await send_group_msg(group_id, at_someone=user_id, text="命令执行失败，请联系管理员")


async def load_bot_names_async() -> Set[str]:
    """异步加载机器人名称列表"""
    file_path = "nfj.txt"

    try:
        if not os.path.exists(file_path):
            logger.warning(f"机器人名单文件 {file_path} 不存在")
            return set()

        # 使用 aiofiles 进行异步文件读取
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()
            names = content.splitlines()
            bot_names_set = set(name.strip().lower() for name in names if name.strip())
            logger.info(f"成功加载 {len(bot_names_set)} 个机器人名称")
            return bot_names_set

    except Exception as e:
        logger.error(f"异步读取机器人名单文件失败: {str(e)}")
        return set()


def count_real_and_bot(team_data: dict, bot_names_set: Set[str]) -> tuple[int, int]:
    """计算团队中真人和机器人数量"""
    players = team_data.get('players', [])
    real_count = 0
    bot_count = 0

    for player in players:
        name = player.get('name', '').strip().lower()
        if name in bot_names_set:
            bot_count += 1
        else:
            real_count += 1

    return real_count, bot_count

# 加载机器人名称的辅助函数
def load_bot_names():
    """加载机器人名称列表"""
    try:
        with file_lock:
            with open('nfj.txt', 'r', encoding='utf-8') as f:
                bot_names = [line.strip().lower() for line in f if line.strip()]
                return set(bot_names)
    except FileNotFoundError:
        logger.error("nfj.txt 文件未找到")
        return set()
    except Exception as e:
        logger.error(f"读取 nfj.txt 失败: {str(e)}")
        return set()

# knf 命令（踢出机器人）
@register_command("knf")
async def cmd_knf(group_id, user_id, args, is_private=False, is_admin=False, message_data=None):
    # 此命令处理器也在global_asyncio_loop线程内运行
    try:
        if not is_admin:
            await send_group_msg(group_id=group_id, at_someone=user_id, text="权限不足。只有管理员才能执行此命令。")
            return
        # 使用带锁的函数加载机器人名称
        bot_names_set = load_bot_names()
        if not bot_names_set:
            error_msg = "错误: 机器人名单为空或读取失败。请检查文件 nfj.txt。"
            logger.warning(error_msg)
            await send_group_msg(group_id=group_id, at_someone=user_id, text=error_msg)
            return

        args_parts = args.strip().split()
        if len(args_parts) < 1:
            await send_group_msg(group_id=group_id, at_someone=user_id, text="请提供服务器号（例如 knf s1）")
            return
        server_key = args_parts[0].lower()
        config = NIUNIUBOT_CONFIG
        our_server_config = config.get("our_server", {})
        if not isinstance(our_server_config, dict) or server_key not in our_server_config:
            available_servers = ', '.join(our_server_config.keys()) if our_server_config else "无"
            await send_group_msg(group_id=group_id, at_someone=user_id,
                                 text=f"无效的服务器号。当前可用服务器: {available_servers}")
            return
        game_id = our_server_config[server_key]
        await send_group_msg(group_id=group_id, text="已接受到踢出暖服机指令，正在处理...", at_someone=user_id)

        # 获取玩家数据或处理异常情况
        players_data = await async_get_players_gametools(game_id=game_id)
        online_bots_names = []

        if not players_data or 'teams' not in players_data:
            # 无法获取玩家数据时，尝试踢出所有已知暖服机
            await send_group_msg(group_id=group_id, at_someone=user_id,
                                 text="无法获取有效的玩家数据，将尝试踢出所有暖服机。")
            online_bots_names = list(bot_names_set)  # 使用所有机器人名称
        else:
            # 正常逻辑：从玩家数据中过滤机器人
            all_players = []
            for team in players_data.get('teams', []):
                for player in team.get('players', []):
                    player_name = player.get('name', '').strip()  # 获取踢出命令的原始名称
                    if player_name:
                        all_players.append(player_name)  # 添加非空名称
            online_bots_names = [name for name in all_players if name.lower() in bot_names_set]

        if not online_bots_names:
            await send_group_msg(group_id=group_id, at_someone=user_id,
                                 text=f"服务器 {server_key} 中没有检测到在线机器人或无机器人名单。")
            return

        # 准备并发送踢出命令
        target_user_id = "3889013937"

        success_count = 0
        failed_bots = []

        # 按顺序发送命令并延迟
        for bot_name in online_bots_names:
            kick_command = f"/kick {bot_name} 暖服机卡机"  # 使用原始名称进行踢出命令
            try:
                # 在nfj_group中发送踢出命令，提及目标机器人ID
                sent_msg_id = await send_private_msg(user_id=target_user_id, text=kick_command)
                if sent_msg_id is not None:
                    success_count += 1
                    logger.info(f"cmd_knf: 成功发送踢出命令{kick_command}")
                else:
                    logger.error(f"cmd_knf: 发送踢出命令失败 {bot_name} ")
                    failed_bots.append(bot_name)
                # 命令间添加1秒延迟
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"cmd_knf: 发送踢出命令失败 for {bot_name}: {str(e)}", exc_info=True)
                failed_bots.append(bot_name)

        # 发送摘要消息
        summary_message = f"尝试发送 {len(online_bots_names)} 个踢出命令。"
        if success_count > 0:
            summary_message += f" 成功发送 {success_count} 个。"
        if failed_bots:
            summary_message += f" 发送失败 {len(failed_bots)} 个: {', '.join(failed_bots)}。"
        await send_group_msg(group_id=group_id, at_someone=user_id, text=summary_message)

    except Exception as e:
        logger.error(f"cmd_knf: 命令执行错误: {str(e)}", exc_info=True)
        await send_group_msg(group_id=group_id, at_someone=user_id, text="命令执行过程中发生错误，请检查日志。")

###############################################################################
#                               暖服机相关_结尾                               #
###############################################################################

###############################################################################
#                                 Flask路由                                   #
###############################################################################

# HTTP回调接口，修改为将任务提交到全局 asyncio 循环
@app.route('/api/event/post', methods=['POST'])
def event_post():
    try:
        data = request.get_json()
        #logger.debug(f"收到事件: {data}") # Debug log can be noisy

        # 在提交任务前等待asyncio循环准备就绪
        if not asyncio_loop_ready.is_set():
             logger.warning("Asyncio loop is not yet ready, skipping message processing.")
             # 返回已接受但未处理，或错误
             return jsonify({"status": "warning", "message": "Asyncio loop not ready"}), 503 # 服务不可用


        if data.get("post_type") == "message":
            # 检查global_asyncio_loop是否正在运行且有效
            if global_asyncio_loop and global_asyncio_loop.is_running():
                # 将handle_message协程提交到全局asyncio循环
                # 从不同线程（Flask线程）使用run_coroutine_threadsafe
                # wrap_future=False表示返回concurrent.futures.Future
                # wrap_future=True（默认）表示返回asyncio.Future
                # 我们不需要立即返回值，所以任一都可以，但wrap_future=False
                # 如果你从不在Flask线程中等待它，可能稍微简单一些。
                # 让我们使用默认值（True）进行标准asyncio Future处理。
                asyncio.run_coroutine_threadsafe(handle_message(data), global_asyncio_loop)

                return jsonify({"status": "ok", "message": "message processing submitted"})
            else:
                logger.error("Global asyncio loop is not running when receiving a message event!")
                # 返回错误状态，表示后端未完全运行
                return jsonify({"status": "error", "message": "Asyncio loop not running"}), 500
        # 对于非消息post_types，只返回ok
        return jsonify({"status": "ok"})

    except Exception as e:
        logger.error(f"处理事件失败: {e}", exc_info=True)
        return jsonify({"status": "failed", "error": str(e)}), 500


# 启动通知函数
def send_startup_notification():
    logger.info("转发机器人服务已启动")
    # 如果需要，你也可以在这里向群组发送消息，但需要asyncio循环
    # if global_asyncio_loop and global_asyncio_loop.is_running():
    #     asyncio.run_coroutine_threadsafe(
    #         send_group_msg(text="转发机器人服务已启动"),
    #         global_asyncio_loop
    #     )
    # else:
    #     logger.warning("Cannot send startup message, asyncio loop not ready.")


# 动态注册新命令：基于 our_server 的所有键
# 这必须在NIUNIUBOT_CONFIG加载之后，app.run之前发生
def register_dynamic_commands(config):
     if "our_server" in config and isinstance(config["our_server"], dict) and config["our_server"]:
         current_server_group_name = config.get("server_group_name", "默认群组名")
         for key in list(config["our_server"].keys()):
             async def dynamic_playerlist_cmd(group_id, user_id, args, is_private=False, is_admin=False, message_data=None, cmd_key=key, srv_group_name=current_server_group_name):
                 # 此动态命令在全局asyncio循环线程中运行
                 try:
                     # 假设3889013937是playerlist的目标机器人ID
                     await send_group_msg(group_id=group_id, text=f"/playerlist {srv_group_name} {cmd_key}",
                                          at_someone=3889013937)
                 except Exception as e:
                     logger.error(f"动态命令 '{cmd_key}' 执行失败: {str(e)}", exc_info=True)
                     if not is_private:
                         await send_group_msg(group_id=group_id, at_someone=user_id, text=f"命令 {cmd_key} 执行失败，请联系管理员")
             # 将新命令注册到 command_handlers
             command_handlers[key] = dynamic_playerlist_cmd
             logger.info(f"动态命令 '{key}' 注册成功")
     else:
         logger.warning("our_server 配置缺失、不是字典或为空，未注册动态命令")

###############################################################################
#                                 主程序启动                                   #
###############################################################################

if __name__ == '__main__':
     # 解析命令行参数
     parser = argparse.ArgumentParser(description="NiuNiuBot Server")
     parser.add_argument('-b', '--bind', default='127.0.0.1', help='绑定地址，默认127.0.0.1')
     parser.add_argument('-p', '--port', type=int, default=18889, help='监听端口，默认18889')
     args = parser.parse_args()

     logger.info(f"使用绑定地址: {args.bind}, 端口: {args.port}")

     # --- 在单独线程中启动专用的asyncio循环 ---
     asyncio_thread = threading.Thread(target=run_asyncio_in_thread)
     asyncio_thread.daemon = True # 允许主线程退出，即使asyncio线程正在运行
     asyncio_thread.start()

     # 在继续之前等待asyncio循环准备就绪
     logger.info("等待 asyncio 事件循环启动...")
     asyncio_loop_ready.wait(timeout=10) # 最多等待10秒

     if not global_asyncio_loop or not global_asyncio_loop.is_running():
          logger.error("未能成功启动 asyncio 事件循环线程！机器人将无法处理异步任务。")
          # 可选择退出或以有限功能继续
          # exit(1) # 如果asyncio是关键的则退出

     # --- 注册动态命令（依赖于已加载的配置） ---
     # 这需要在配置加载后但启动Flask应用前完成
     register_dynamic_commands(NIUNIUBOT_CONFIG)


     logger.info("正在启动 Flask web 服务...")

     # 启动 Flask 服务器，启用多线程模式
     # 重要：使用单独线程和asyncio时，禁用Flask的重新加载器
     # 因为它可能导致脚本运行两次并创建多个循环/线程。
     app.run(host=args.bind, port=args.port, threaded=True, debug=False, use_reloader=False)

     # app.run下面的代码只有在Flask服务器停止时才会到达。
     logger.info("Flask web 服务已停止。")

     # 可选：如果Flask先停止，则向asyncio线程发送停止信号
     # 这更复杂，通常由OS信号（如Ctrl+C）处理，
     # asyncio循环应该捕获这些信号来设置其停止事件。
     # 为简单起见，依赖daemon=True可能足以进行基本关闭。
     # 要实现优雅关闭，你需要在主线程中添加信号处理器
     # 或asyncio线程来设置main_asyncio_tasks中使用的stop_event。
